/**
 * Architectural Excellence Test Suite
 * Comprehensive tests for Week 3 & 4 implementations
 */

import { createNetworkError, createValidationError, errorManager } from '../../src/services/errorHandling/ErrorManager';
import { isOnline, offlineManager, OperationType, queueOfflineOperation } from '../../src/services/offline/OfflineManager';
import { errorRecoveryService, recoverFromError } from '../../src/services/recovery/ErrorRecoveryService';
import { validateEmail, validatePassword, validationEngine, ValidationType } from '../../src/services/validation/ValidationEngine';
import { accessibilityManager, AccessibilityRole, getAccessibilityProps } from '../../src/shared/services/accessibility/accessibilityManager';
import { enterpriseLogger, error, info, logUserAction } from '../../src/shared/services/logging/enterpriseLogger';
import { configManager, getValue, isFeatureEnabled } from '../../src/shared/services/system/configurationManager';

// Mock external dependencies
jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({
    isConnected: true,
    type: 'wifi',
    isInternetReachable: true
  })),
  addEventListener: jest.fn()
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([]))
}));

jest.mock('react-native', () => ({
  AccessibilityInfo: {
    isScreenReaderEnabled: jest.fn(() => Promise.resolve(false)),
    isReduceMotionEnabled: jest.fn(() => Promise.resolve(false)),
    addEventListener: jest.fn(),
    announceForAccessibility: jest.fn(),
    setAccessibilityFocus: jest.fn()
  },
  Platform: {
    OS: 'ios'
  }
}));

describe('Week 3: Architectural Excellence', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Error Handling System', () => {
    it('should create and handle network errors properly', async () => {
      const networkError = createNetworkError(
        'Connection timeout',
        'NETWORK_TIMEOUT',
        true,
        { component: 'DataService', action: 'fetchData' }
      );

      expect(networkError.code).toBe('NETWORK_TIMEOUT');
      expect(networkError.category).toBe('network');
      expect(networkError.isRetryable).toBe(true);
      expect(networkError.userMessage).toContain('Connection timed out');

      // Test error handling
      await errorManager.handleError(networkError);

      // Verify error was processed
      expect(networkError.context.timestamp).toBeDefined();
    });

    it('should create and handle validation errors properly', async () => {
      const validationError = createValidationError(
        'Invalid email format',
        'email',
        'invalid-email',
        { component: 'LoginForm' }
      );

      expect(validationError.code).toBe('VALIDATION_EMAIL_INVALID');
      expect(validationError.category).toBe('validation');
      expect(validationError.isRetryable).toBe(false);

      await errorManager.handleError(validationError);
    });

    it('should provide error statistics', () => {
      const stats = errorManager.getErrorStats();

      expect(stats).toHaveProperty('totalErrors');
      expect(stats).toHaveProperty('errorsByCategory');
      expect(stats).toHaveProperty('errorsBySeverity');
      expect(stats).toHaveProperty('topErrors');
    });
  });

  describe('Input Validation System', () => {
    it('should validate email addresses correctly', () => {
      const validEmail = validateEmail('<EMAIL>');
      expect(validEmail.isValid).toBe(true);
      expect(validEmail.sanitizedValue).toBe('<EMAIL>');

      const invalidEmail = validateEmail('invalid-email');
      expect(invalidEmail.isValid).toBe(false);
      expect(invalidEmail.errors).toHaveLength(1);
      expect(typeof invalidEmail.errors[0] === 'object' ? invalidEmail.errors[0].code : invalidEmail.errors[0]).toBe('INVALID_EMAIL');
    });

    it('should validate passwords with proper requirements', () => {
      const validPassword = validatePassword('SecurePass123!');
      expect(validPassword.isValid).toBe(true);

      const weakPassword = validatePassword('weak');
      expect(weakPassword.isValid).toBe(false);
      expect(typeof weakPassword.errors[0] === 'object' ? weakPassword.errors[0].code : weakPassword.errors[0]).toBe('WEAK_PASSWORD');
    });

    it('should validate complex objects with schemas', () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        age: 25
      };

      const schema = {
        email: { type: ValidationType.EMAIL, required: true, sanitize: true },
        firstName: { type: ValidationType.STRING, required: true, minLength: 1, maxLength: 50 },
        lastName: { type: ValidationType.STRING, required: true, minLength: 1, maxLength: 50 },
        age: { type: ValidationType.INTEGER, required: false, min: 18, max: 120 }
      };

      const result = validationEngine.validateObject(userData, schema);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBeDefined();
    });

    it('should handle validation errors gracefully', () => {
      const invalidData = {
        email: 'invalid-email',
        firstName: '',
        age: 15
      };

      const schema = {
        email: { type: ValidationType.EMAIL, required: true },
        firstName: { type: ValidationType.STRING, required: true, minLength: 1 },
        age: { type: ValidationType.INTEGER, min: 18 }
      };

      const result = validationEngine.validateObject(invalidData, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Configuration Management System', () => {
    it('should provide configuration values', () => {
      const environment = configManager.get('environment');
      expect(environment).toBeDefined();

      const version = getValue<string>('version');
      expect(version).toBeDefined();
    });

    it('should handle feature flags', () => {
      const offlineEnabled = isFeatureEnabled('enableOfflineMode');
      expect(typeof offlineEnabled).toBe('boolean');

      const analyticsEnabled = isFeatureEnabled('enableAnalytics');
      expect(typeof analyticsEnabled).toBe('boolean');
    });

    it('should provide environment-specific settings', () => {
      expect(typeof configManager.isDevelopment()).toBe('boolean');
      expect(typeof configManager.isProduction()).toBe('boolean');
      expect(typeof configManager.isStaging()).toBe('boolean');
    });

    it('should provide external service configurations', () => {
      const supabaseConfig = configManager.getSupabaseConfig();
      expect(supabaseConfig).toHaveProperty('url');
      expect(supabaseConfig).toHaveProperty('anonKey');

      const analyticsConfig = configManager.getAnalyticsConfig();
      expect(analyticsConfig).toHaveProperty('enabled');
      expect(analyticsConfig).toHaveProperty('debug');
    });
  });

  describe('Enterprise Logging System', () => {
    it('should log messages with different levels', () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation();

      info('Test info message', { test: 'data' });
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should log user actions', () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation();

      logUserAction('button_click', 'submit');
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle error logging', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const testError = new Error('Test error');
      error('Test error occurred', testError);
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should provide logging statistics', () => {
      const stats = enterpriseLogger.getStats();
      expect(stats).toHaveProperty('totalLogs');
      expect(stats).toHaveProperty('logsByLevel');
      expect(stats).toHaveProperty('logsByCategory');
    });
  });
});

describe('Week 4: Professional Polish', () => {
  describe('Error Recovery System', () => {
    it('should attempt recovery for network errors', async () => {
      const networkError = createNetworkError(
        'Connection failed',
        'NETWORK_ERROR',
        true,
        { component: 'ApiService' }
      );

      const result = await recoverFromError(networkError);
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('strategy');
      expect(result).toHaveProperty('message');
    });

    it('should provide recovery statistics', () => {
      const stats = errorRecoveryService.getRecoveryStats();
      expect(stats).toHaveProperty('totalRecoveries');
      expect(stats).toHaveProperty('successRate');
      expect(stats).toHaveProperty('strategiesUsed');
      expect(stats).toHaveProperty('mostCommonErrors');
    });
  });

  describe('Offline Management System', () => {
    it('should detect network status', () => {
      const networkState = offlineManager.getNetworkState();
      expect(networkState).toHaveProperty('isConnected');
      expect(networkState).toHaveProperty('type');
      expect(networkState).toHaveProperty('quality');

      const online = isOnline();
      expect(typeof online).toBe('boolean');
    });

    it('should queue operations for offline sync', async () => {
      await queueOfflineOperation(
        OperationType.CREATE,
        { title: 'Test Event', description: 'Test Description' },
        3
      );

      // Verify the operation was queued by checking offline stats
      const stats = offlineManager.getOfflineStats();
      expect(stats).toBeDefined();
    });

    it('should provide offline statistics', () => {
      const stats = offlineManager.getOfflineStats();
      expect(stats).toHaveProperty('queueSize');
      expect(stats).toHaveProperty('pendingOperations');
      expect(stats).toHaveProperty('networkQuality');
    });

    it('should handle cache operations', async () => {
      await offlineManager.cacheData('test-key', { data: 'test' }, 60000);

      const cachedData = await offlineManager.getCachedData('test-key');
      expect(cachedData).toEqual({ data: 'test' });
    });
  });

  describe('Accessibility Management System', () => {
    it('should provide accessibility settings', () => {
      const settings = accessibilityManager.getSettings();
      expect(settings).toHaveProperty('screenReaderEnabled');
      expect(settings).toHaveProperty('reduceMotionEnabled');
      expect(settings).toHaveProperty('fontSize');
    });

    it('should generate accessibility props', () => {
      const props = getAccessibilityProps(
        AccessibilityRole.BUTTON,
        'Submit Button',
        'Submits the form',
        { disabled: false }
      );

      expect(props).toHaveProperty('accessible');
      expect(props).toHaveProperty('accessibilityRole');
      expect(props).toHaveProperty('accessibilityLabel');
      expect(props).toHaveProperty('accessibilityHint');
      expect(props).toHaveProperty('accessibilityState');
    });

    it('should calculate color contrast ratios', () => {
      const contrast = accessibilityManager.calculateColorContrast('#000000', '#FFFFFF');
      expect(contrast).toHaveProperty('ratio');
      expect(contrast).toHaveProperty('level');
      expect(contrast).toHaveProperty('passes');
      expect(contrast.ratio).toBeGreaterThan(7); // Should be high contrast
    });

    it('should audit components for accessibility', () => {
      const mockProps = {
        accessibilityLabel: 'Test Button',
        accessibilityRole: 'button',
        onPress: jest.fn()
      };

      const audit = accessibilityManager.auditComponent('TestButton', mockProps);
      expect(audit).toHaveProperty('component');
      expect(audit).toHaveProperty('issues');
      expect(audit).toHaveProperty('score');
      expect(audit).toHaveProperty('level');
    });

    it('should handle screen reader announcements', () => {
      // This would normally interact with the platform's accessibility API
      accessibilityManager.announce('Test announcement');
      // In a real test, we'd verify the platform API was called
    });

    it('should provide accessible animation durations', () => {
      const normalDuration = accessibilityManager.getAnimationDuration(300);
      expect(typeof normalDuration).toBe('number');
      expect(normalDuration).toBeGreaterThanOrEqual(0);
    });
  });
});

describe('System Integration', () => {
  it('should integrate error handling with recovery', async () => {
    const error = createNetworkError('Test error', 'TEST_ERROR');

    // Handle error through error manager
    await errorManager.handleError(error);

    // Attempt recovery
    const recovery = await recoverFromError(error);
    expect(recovery).toBeDefined();
  });

  it('should integrate validation with error handling', async () => {
    const invalidData = { email: 'invalid' };
    const schema = { email: { type: ValidationType.EMAIL, required: true } };

    const validation = validationEngine.validateObject(invalidData, schema);

    if (!validation.isValid) {
      const validationError = createValidationError(
        validation.errors[0].message,
        validation.errors[0].field,
        invalidData.email
      );

      await errorManager.handleError(validationError);
    }
  });

  it('should integrate configuration with feature flags', () => {
    const offlineEnabled = isFeatureEnabled('enableOfflineMode');
    const offlineManagerEnabled = offlineManager.isOfflineModeEnabled();

    // These should be consistent
    expect(typeof offlineEnabled).toBe('boolean');
    expect(typeof offlineManagerEnabled).toBe('boolean');
  });

  it('should integrate logging across all systems', () => {
    const consoleSpy = jest.spyOn(console, 'info').mockImplementation();

    // All systems should use the enterprise logger
    info('Integration test message');

    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});

describe('Production Readiness', () => {
  it('should handle high-volume operations', async () => {
    const operations = Array.from({ length: 100 }, (_, i) => ({
      type: OperationType.CREATE,
      entity: 'test_entity',
      data: { id: i, name: `Test ${i}` },
      priority: 1,
      maxRetries: 3
    }));

    const promises = operations.map(op => queueOfflineOperation(op));
    const results = await Promise.all(promises);

    expect(results).toHaveLength(100);
    results.forEach(id => expect(typeof id).toBe('string'));
  });

  it('should maintain performance under load', () => {
    const startTime = Date.now();

    // Perform multiple validation operations
    for (let i = 0; i < 1000; i++) {
      validateEmail(`user${i}@example.com`);
    }

    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(1000); // Should complete in under 1 second
  });

  it('should handle memory efficiently', () => {
    const initialMemory = process.memoryUsage?.()?.heapUsed || 0;

    // Create many objects
    const objects = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      data: `test data ${i}`,
      timestamp: Date.now()
    }));

    // Clear references
    objects.length = 0;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    const finalMemory = process.memoryUsage?.()?.heapUsed || 0;
    const memoryIncrease = finalMemory - initialMemory;

    // Memory increase should be reasonable
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB
  });

  it('should provide comprehensive error information', async () => {
    const error = createNetworkError(
      'Test production error',
      'PROD_TEST_ERROR',
      true,
      {
        component: 'ProductionTest',
        action: 'testOperation',
        userId: 'test-user-123',
        sessionId: 'test-session-456'
      }
    );

    await errorManager.handleError(error);

    expect(error.context.timestamp).toBeDefined();
    expect(error.context.component).toBe('ProductionTest');
    expect(error.context.userId).toBe('test-user-123');
    expect(error.userMessage).toBeDefined();
  });
});
