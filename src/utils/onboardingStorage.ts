/**
 * Onboarding Storage Utility
 * Manages onboarding state and progress
 */

export interface OnboardingStep {
  id: string;
  name: string;
  completed: boolean;
  completedAt?: Date;
  data?: any;
}

export interface OnboardingState {
  isCompleted: boolean;
  currentStep: string | null;
  steps: OnboardingStep[];
  startedAt?: Date;
  completedAt?: Date;
}

const STORAGE_KEY = 'onboarding_state';

const defaultSteps: OnboardingStep[] = [
  { id: 'welcome', name: 'Welcome', completed: false },
  { id: 'profile_setup', name: 'Profile Setup', completed: false },
  { id: 'partner_connection', name: 'Partner Connection', completed: false },
  { id: 'preferences', name: 'Preferences', completed: false },
  { id: 'first_question', name: 'First Question', completed: false },
  { id: 'tutorial', name: 'Tutorial', completed: false }
];

class OnboardingStorage {
  private state: OnboardingState = {
    isCompleted: false,
    currentStep: null,
    steps: [...defaultSteps]
  };

  async loadState(): Promise<OnboardingState> {
    try {
      // In React Native, this would use AsyncStorage
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);

        // Convert date strings back to Date objects
        if (parsedState.startedAt) {
          parsedState.startedAt = new Date(parsedState.startedAt);
        }
        if (parsedState.completedAt) {
          parsedState.completedAt = new Date(parsedState.completedAt);
        }

        parsedState.steps = parsedState.steps.map((step: any) => ({
          ...step,
          completedAt: step.completedAt ? new Date(step.completedAt) : undefined
        }));

        this.state = { ...this.state, ...parsedState };
      }
    } catch (error) {
      console.error('Failed to load onboarding state:', error);
    }

    return this.state;
  }

  async saveState(): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.state));
    } catch (error) {
      console.error('Failed to save onboarding state:', error);
    }
  }

  async startOnboarding(): Promise<void> {
    this.state.startedAt = new Date();
    this.state.currentStep = this.state.steps[0]?.id || null;
    await this.saveState();
  }

  async completeStep(stepId: string, data?: any): Promise<boolean> {
    const stepIndex = this.state.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) return false;

    this.state.steps[stepIndex] = {
      ...this.state.steps[stepIndex],
      completed: true,
      completedAt: new Date(),
      data
    };

    // Move to next step
    const nextStepIndex = stepIndex + 1;
    if (nextStepIndex < this.state.steps.length) {
      this.state.currentStep = this.state.steps[nextStepIndex].id;
    } else {
      // All steps completed
      this.state.isCompleted = true;
      this.state.completedAt = new Date();
      this.state.currentStep = null;
    }

    await this.saveState();
    return true;
  }

  async skipStep(stepId: string): Promise<boolean> {
    return await this.completeStep(stepId, { skipped: true });
  }

  async goToStep(stepId: string): Promise<boolean> {
    const step = this.state.steps.find(s => s.id === stepId);
    if (!step) return false;

    this.state.currentStep = stepId;
    await this.saveState();
    return true;
  }

  async resetOnboarding(): Promise<void> {
    this.state = {
      isCompleted: false,
      currentStep: null,
      steps: defaultSteps.map(step => ({ ...step, completed: false, completedAt: undefined }))
    };
    await this.saveState();
  }

  getCurrentStep(): OnboardingStep | null {
    if (!this.state.currentStep) return null;
    return this.state.steps.find(step => step.id === this.state.currentStep) || null;
  }

  getNextStep(): OnboardingStep | null {
    const currentIndex = this.state.steps.findIndex(step => step.id === this.state.currentStep);
    if (currentIndex === -1 || currentIndex >= this.state.steps.length - 1) return null;
    return this.state.steps[currentIndex + 1];
  }

  getPreviousStep(): OnboardingStep | null {
    const currentIndex = this.state.steps.findIndex(step => step.id === this.state.currentStep);
    if (currentIndex <= 0) return null;
    return this.state.steps[currentIndex - 1];
  }

  getProgress(): { completed: number; total: number; percentage: number } {
    const completed = this.state.steps.filter(step => step.completed).length;
    const total = this.state.steps.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { completed, total, percentage };
  }

  isStepCompleted(stepId: string): boolean {
    const step = this.state.steps.find(s => s.id === stepId);
    return step?.completed || false;
  }

  getStepData(stepId: string): any {
    const step = this.state.steps.find(s => s.id === stepId);
    return step?.data;
  }

  async updateStepData(stepId: string, data: any): Promise<boolean> {
    const stepIndex = this.state.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) return false;

    this.state.steps[stepIndex] = {
      ...this.state.steps[stepIndex],
      data: { ...this.state.steps[stepIndex].data, ...data }
    };

    await this.saveState();
    return true;
  }

  getState(): OnboardingState {
    return { ...this.state };
  }
}

// Singleton instance
const onboardingStorage = new OnboardingStorage();

// Convenience functions
export const loadOnboardingState = () => onboardingStorage.loadState();
export const startOnboarding = () => onboardingStorage.startOnboarding();
export const completeStep = (stepId: string, data?: any) => onboardingStorage.completeStep(stepId, data);
export const skipStep = (stepId: string) => onboardingStorage.skipStep(stepId);
export const goToStep = (stepId: string) => onboardingStorage.goToStep(stepId);
export const resetOnboarding = () => onboardingStorage.resetOnboarding();
export const getCurrentStep = () => onboardingStorage.getCurrentStep();
export const getNextStep = () => onboardingStorage.getNextStep();
export const getPreviousStep = () => onboardingStorage.getPreviousStep();
export const getOnboardingProgress = () => onboardingStorage.getProgress();
export const isStepCompleted = (stepId: string) => onboardingStorage.isStepCompleted(stepId);
export const getStepData = (stepId: string) => onboardingStorage.getStepData(stepId);
export const updateStepData = (stepId: string, data: any) => onboardingStorage.updateStepData(stepId, data);
export const getOnboardingState = () => onboardingStorage.getState();

// Convenience function for checking if onboarding is completed
export const hasCompletedOnboarding = async (): Promise<boolean> => {
  const state = await onboardingStorage.loadState();
  return state.isCompleted;
};

// Convenience function for marking onboarding as completed
export const markOnboardingCompleted = async (): Promise<void> => {
  const state = await onboardingStorage.loadState();
  state.isCompleted = true;
  state.completedAt = new Date();
  // Mark all steps as completed
  state.steps.forEach(step => {
    if (!step.completed) {
      step.completed = true;
      step.completedAt = new Date();
    }
  });
  await onboardingStorage.saveState();
};

export default onboardingStorage;
