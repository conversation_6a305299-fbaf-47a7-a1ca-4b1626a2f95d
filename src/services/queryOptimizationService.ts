/**
 * Query Optimization Service
 * Optimizes database queries and caching strategies
 */

export interface QueryMetrics {
  queryId: string;
  executionTime: number;
  cacheHit: boolean;
  rowsReturned: number;
  timestamp: Date;
}

export interface OptimizationRule {
  name: string;
  condition: (query: string) => boolean;
  optimize: (query: string) => string;
  priority: number;
}

export interface CacheEntry {
  key: string;
  data: any;
  expiry: Date;
  hitCount: number;
  lastAccessed: Date;
}

export interface QueryStats {
  totalQueries: number;
  cacheHitRate: number;
  averageExecutionTime: number;
  slowQueries: QueryMetrics[];
  topQueries: { query: string; count: number }[];
}

class QueryOptimizationService {
  private cache: Map<string, CacheEntry> = new Map();
  private queryMetrics: QueryMetrics[] = [];
  private optimizationRules: OptimizationRule[] = [];
  private queryCounter: Map<string, number> = new Map();

  constructor() {
    this.initializeOptimizationRules();
  }

  private initializeOptimizationRules(): void {
    // Add common optimization rules
    this.optimizationRules.push({
      name: 'AddIndexHints',
      condition: (query) => query.includes('WHERE') && !query.includes('INDEX'),
      optimize: (query) => {
        // Simple optimization: suggest using indexes
        return query.replace('SELECT', 'SELECT /*+ USE_INDEX */');
      },
      priority: 1
    });

    this.optimizationRules.push({
      name: 'LimitResults',
      condition: (query) => query.includes('SELECT') && !query.includes('LIMIT'),
      optimize: (query) => {
        // Add LIMIT if not present
        return `${query} LIMIT 1000`;
      },
      priority: 2
    });

    this.optimizationRules.push({
      name: 'OptimizeJoins',
      condition: (query) => query.includes('JOIN') && query.includes('SELECT *'),
      optimize: (query) => {
        // Replace SELECT * with specific columns in joins
        return query.replace('SELECT *', 'SELECT t1.id, t1.name');
      },
      priority: 3
    });
  }

  optimizeQuery(query: string): string {
    let optimizedQuery = query;

    // Apply optimization rules in priority order
    const applicableRules = this.optimizationRules
      .filter(rule => rule.condition(query))
      .sort((a, b) => a.priority - b.priority);

    for (const rule of applicableRules) {
      optimizedQuery = rule.optimize(optimizedQuery);
    }

    return optimizedQuery;
  }

  async executeQuery(query: string, useCache: boolean = true): Promise<any> {
    const queryId = this.generateQueryId(query);
    const cacheKey = this.generateCacheKey(query);

    // Check cache first
    if (useCache && this.cache.has(cacheKey)) {
      const cacheEntry = this.cache.get(cacheKey)!;

      if (cacheEntry.expiry > new Date()) {
        cacheEntry.hitCount++;
        cacheEntry.lastAccessed = new Date();

        // Record cache hit
        this.recordQueryMetrics({
          queryId,
          executionTime: 0,
          cacheHit: true,
          rowsReturned: Array.isArray(cacheEntry.data) ? cacheEntry.data.length : 1,
          timestamp: new Date()
        });

        return cacheEntry.data;
      } else {
        // Remove expired entry
        this.cache.delete(cacheKey);
      }
    }

    // Execute query (simulated)
    const startTime = Date.now();
    const optimizedQuery = this.optimizeQuery(query);

    // Simulate query execution
    const mockData = this.simulateQueryExecution(optimizedQuery);
    const executionTime = Date.now() - startTime;

    // Cache the result
    if (useCache) {
      this.cacheResult(cacheKey, mockData, 300000); // 5 minutes TTL
    }

    // Record metrics
    this.recordQueryMetrics({
      queryId,
      executionTime,
      cacheHit: false,
      rowsReturned: Array.isArray(mockData) ? mockData.length : 1,
      timestamp: new Date()
    });

    // Update query counter
    this.queryCounter.set(query, (this.queryCounter.get(query) || 0) + 1);

    return mockData;
  }

  private simulateQueryExecution(query: string): any {
    // Simulate different types of queries
    if (query.includes('SELECT')) {
      return [
        { id: 1, name: 'Sample Data 1' },
        { id: 2, name: 'Sample Data 2' }
      ];
    } else if (query.includes('INSERT')) {
      return { insertId: Math.floor(Math.random() * 1000) };
    } else if (query.includes('UPDATE')) {
      return { affectedRows: Math.floor(Math.random() * 10) };
    } else if (query.includes('DELETE')) {
      return { deletedRows: Math.floor(Math.random() * 5) };
    }

    return { success: true };
  }

  private generateQueryId(query: string): string {
    return `query_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(query: string): string {
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `cache_${Math.abs(hash)}`;
  }

  private cacheResult(key: string, data: any, ttlMs: number): void {
    const expiry = new Date(Date.now() + ttlMs);
    this.cache.set(key, {
      key,
      data,
      expiry,
      hitCount: 0,
      lastAccessed: new Date()
    });
  }

  private recordQueryMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics);

    // Keep only last 1000 metrics
    if (this.queryMetrics.length > 1000) {
      this.queryMetrics.shift();
    }
  }

  getQueryStats(): QueryStats {
    const totalQueries = this.queryMetrics.length;
    const cacheHits = this.queryMetrics.filter(m => m.cacheHit).length;
    const cacheHitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;

    const totalExecutionTime = this.queryMetrics.reduce((sum, m) => sum + m.executionTime, 0);
    const averageExecutionTime = totalQueries > 0 ? totalExecutionTime / totalQueries : 0;

    const slowQueries = this.queryMetrics
      .filter(m => m.executionTime > 100) // Queries taking more than 100ms
      .sort((a, b) => b.executionTime - a.executionTime)
      .slice(0, 10);

    const topQueries = Array.from(this.queryCounter.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalQueries,
      cacheHitRate,
      averageExecutionTime,
      slowQueries,
      topQueries
    };
  }

  clearCache(): void {
    this.cache.clear();
  }

  clearMetrics(): void {
    this.queryMetrics = [];
    this.queryCounter.clear();
  }

  addOptimizationRule(rule: OptimizationRule): void {
    this.optimizationRules.push(rule);
    this.optimizationRules.sort((a, b) => a.priority - b.priority);
  }

  getCacheStats(): { size: number; hitRate: number; entries: CacheEntry[] } {
    const entries = Array.from(this.cache.values());
    const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0);
    const hitRate = entries.length > 0 ? totalHits / entries.length : 0;

    return {
      size: this.cache.size,
      hitRate,
      entries: entries.slice(0, 20) // Return top 20 entries
    };
  }

  async getOptimizedUserData(userId: string, options?: { includePartner?: boolean; includeHistory?: boolean }) {
    const { includePartner = false, includeHistory = false } = options || {};

    try {
      // Simulate optimized user data query with joins
      const baseQuery = `SELECT * FROM users WHERE id = '${userId}'`;
      let optimizedQuery = baseQuery;

      if (includePartner) {
        optimizedQuery += ` LEFT JOIN couples ON users.id = couples.user_id LEFT JOIN users partner ON couples.partner_id = partner.id`;
      }

      if (includeHistory) {
        optimizedQuery += ` LEFT JOIN user_events ON users.id = user_events.user_id`;
      }

      const result = await this.executeQuery(optimizedQuery);

      return {
        user: {
          id: userId,
          name: `User ${userId}`,
          email: `user${userId}@example.com`,
          createdAt: new Date().toISOString()
        },
        partner: includePartner ? {
          id: `partner_${userId}`,
          name: `Partner of ${userId}`,
          email: `partner${userId}@example.com`
        } : null,
        history: includeHistory ? [
          { event: 'profile_created', timestamp: new Date().toISOString() },
          { event: 'daily_question_answered', timestamp: new Date().toISOString() }
        ] : null,
        queryTime: Math.random() * 50 + 10 // Simulated query time
      };
    } catch (error) {
      console.error('Error getting optimized user data:', error);
      throw error;
    }
  }

  async getOptimizedDateNightData(coupleId: string, options?: { limit?: number; includeHistory?: boolean }) {
    const { limit = 10, includeHistory = false } = options || {};

    try {
      // Simulate optimized date night data query
      const baseQuery = `SELECT * FROM date_nights WHERE couple_id = '${coupleId}' ORDER BY created_at DESC LIMIT ${limit}`;
      let optimizedQuery = baseQuery;

      if (includeHistory) {
        optimizedQuery += ` UNION SELECT * FROM date_night_history WHERE couple_id = '${coupleId}' ORDER BY created_at DESC LIMIT ${limit}`;
      }

      const result = await this.executeQuery(optimizedQuery);

      // Generate sample date night data
      const dateNights = Array.from({ length: Math.min(limit, 5) }, (_, i) => ({
        id: `date_night_${i + 1}`,
        coupleId,
        title: `Date Night ${i + 1}`,
        description: `A wonderful evening together`,
        scheduledDate: new Date(Date.now() + (i * 24 * 60 * 60 * 1000)).toISOString(),
        status: i === 0 ? 'upcoming' : 'completed',
        rating: i > 0 ? Math.floor(Math.random() * 5) + 1 : null,
        createdAt: new Date().toISOString()
      }));

      return {
        dateNights,
        total: dateNights.length,
        upcoming: dateNights.filter(dn => dn.status === 'upcoming').length,
        completed: dateNights.filter(dn => dn.status === 'completed').length,
        averageRating: dateNights
          .filter(dn => dn.rating)
          .reduce((sum, dn) => sum + (dn.rating || 0), 0) / dateNights.filter(dn => dn.rating).length || 0,
        queryTime: Math.random() * 30 + 5 // Simulated query time
      };
    } catch (error) {
      console.error('Error getting optimized date night data:', error);
      throw error;
    }
  }
}

// Singleton instance
export const queryOptimizationService = new QueryOptimizationService();
