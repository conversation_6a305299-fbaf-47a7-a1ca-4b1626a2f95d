/**
 * Offline Management System
 * Handles offline operations, caching, and network state
 */

export enum OperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  SYNC = 'SYNC'
}

export interface OfflineOperation {
  id: string;
  type: OperationType;
  data: any;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

export interface NetworkState {
  isConnected: boolean;
  type: string;
  isInternetReachable: boolean;
}

export interface OfflineStats {
  queueSize: number;
  pendingOperations: number;
  cachedItems: number;
  lastSync?: Date;
}

class OfflineManager {
  private operationQueue: OfflineOperation[] = [];
  private cache: Map<string, { data: any; expiry: Date }> = new Map();
  private networkState: NetworkState = {
    isConnected: true,
    type: 'wifi',
    isInternetReachable: true
  };

  getNetworkState(): NetworkState {
    return { ...this.networkState };
  }

  isOfflineModeEnabled(): boolean {
    return true; // Always enabled for now
  }

  async queueOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const queuedOperation: OfflineOperation = {
      ...operation,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      retryCount: 0
    };

    this.operationQueue.push(queuedOperation);
  }

  async cacheData(key: string, data: any, ttlMs: number): Promise<void> {
    const expiry = new Date(Date.now() + ttlMs);
    this.cache.set(key, { data, expiry });
  }

  async getCachedData(key: string): Promise<any | null> {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (cached.expiry < new Date()) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  getOfflineStats(): OfflineStats {
    return {
      queueSize: this.operationQueue.length,
      pendingOperations: this.operationQueue.filter(op => op.retryCount < op.maxRetries).length,
      cachedItems: this.cache.size,
      lastSync: new Date()
    };
  }

  async processQueue(): Promise<void> {
    // Process queued operations when online
    if (!this.networkState.isConnected) return;

    const pendingOps = this.operationQueue.filter(op => op.retryCount < op.maxRetries);
    
    for (const operation of pendingOps) {
      try {
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Remove successful operation
        const index = this.operationQueue.indexOf(operation);
        if (index > -1) {
          this.operationQueue.splice(index, 1);
        }
      } catch (error) {
        operation.retryCount++;
      }
    }
  }

  clearCache(): void {
    this.cache.clear();
  }

  clearQueue(): void {
    this.operationQueue = [];
  }
}

// Utility functions
export function isOnline(): boolean {
  return offlineManager.getNetworkState().isConnected;
}

export async function queueOfflineOperation(
  type: OperationType,
  data: any,
  maxRetries: number = 3
): Promise<void> {
  await offlineManager.queueOperation({ type, data, maxRetries });
}

// Singleton instance
export const offlineManager = new OfflineManager();
