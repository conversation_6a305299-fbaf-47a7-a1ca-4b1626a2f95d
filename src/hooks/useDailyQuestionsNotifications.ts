/**
 * Daily Questions Notifications Hook
 * Manages notification settings for daily questions
 */

import { useState, useEffect } from 'react';

export interface NotificationSettings {
  enabled: boolean;
  time: string; // HH:MM format
  days: string[]; // ['monday', 'tuesday', etc.]
  reminderType: 'push' | 'email' | 'both';
  partnerNotifications: boolean;
  streakReminders: boolean;
}

export interface NotificationPermissions {
  granted: boolean;
  denied: boolean;
  canRequest: boolean;
}

const defaultSettings: NotificationSettings = {
  enabled: true,
  time: '19:00',
  days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
  reminderType: 'push',
  partnerNotifications: true,
  streakReminders: true
};

export function useDailyQuestionsNotifications() {
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [permissions, setPermissions] = useState<NotificationPermissions>({
    granted: false,
    denied: false,
    canRequest: true
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
    checkPermissions();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      // Simulate loading from storage
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // In a real app, this would load from AsyncStorage or similar
      const stored = localStorage.getItem('daily_questions_notifications');
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: Partial<NotificationSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);
      
      // Save to storage
      localStorage.setItem('daily_questions_notifications', JSON.stringify(updatedSettings));
      
      // Schedule notifications if enabled
      if (updatedSettings.enabled) {
        await scheduleNotifications(updatedSettings);
      } else {
        await cancelNotifications();
      }
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  };

  const checkPermissions = async () => {
    try {
      // Check notification permissions
      if ('Notification' in window) {
        const permission = Notification.permission;
        setPermissions({
          granted: permission === 'granted',
          denied: permission === 'denied',
          canRequest: permission === 'default'
        });
      } else {
        setPermissions({
          granted: false,
          denied: true,
          canRequest: false
        });
      }
    } catch (error) {
      console.error('Failed to check permissions:', error);
    }
  };

  const requestPermissions = async (): Promise<boolean> => {
    try {
      if ('Notification' in window && Notification.permission === 'default') {
        const permission = await Notification.requestPermission();
        const granted = permission === 'granted';
        
        setPermissions({
          granted,
          denied: permission === 'denied',
          canRequest: false
        });
        
        return granted;
      }
      return permissions.granted;
    } catch (error) {
      console.error('Failed to request permissions:', error);
      return false;
    }
  };

  const scheduleNotifications = async (notificationSettings: NotificationSettings) => {
    try {
      // In a real app, this would schedule local notifications
      console.log('Scheduling notifications:', notificationSettings);
      
      // For web, we might use service workers or a scheduling service
      // For React Native, we'd use @react-native-async-storage/async-storage
      // and react-native-push-notification or similar
      
      if (notificationSettings.enabled && permissions.granted) {
        // Schedule daily notification
        const [hours, minutes] = notificationSettings.time.split(':').map(Number);
        console.log(`Notifications scheduled for ${hours}:${minutes} on days:`, notificationSettings.days);
      }
    } catch (error) {
      console.error('Failed to schedule notifications:', error);
    }
  };

  const cancelNotifications = async () => {
    try {
      console.log('Cancelling all daily question notifications');
      // In a real app, this would cancel scheduled notifications
    } catch (error) {
      console.error('Failed to cancel notifications:', error);
    }
  };

  const testNotification = async () => {
    try {
      if (!permissions.granted) {
        const granted = await requestPermissions();
        if (!granted) return false;
      }

      // Send test notification
      new Notification('Daily Questions', {
        body: "Here's your daily question for today!",
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png'
      });

      return true;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      return false;
    }
  };

  const updateTime = (time: string) => {
    saveSettings({ time });
  };

  const updateDays = (days: string[]) => {
    saveSettings({ days });
  };

  const toggleEnabled = () => {
    saveSettings({ enabled: !settings.enabled });
  };

  const updateReminderType = (reminderType: 'push' | 'email' | 'both') => {
    saveSettings({ reminderType });
  };

  const togglePartnerNotifications = () => {
    saveSettings({ partnerNotifications: !settings.partnerNotifications });
  };

  const toggleStreakReminders = () => {
    saveSettings({ streakReminders: !settings.streakReminders });
  };

  return {
    settings,
    permissions,
    isLoading,
    updateTime,
    updateDays,
    toggleEnabled,
    updateReminderType,
    togglePartnerNotifications,
    toggleStreakReminders,
    requestPermissions,
    testNotification,
    saveSettings
  };
}
