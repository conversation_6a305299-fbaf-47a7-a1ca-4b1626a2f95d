/**
 * User Events Hook
 * Manages user events and activity tracking
 */

import { useEffect, useState } from 'react';

export interface UserEvent {
  id: string;
  userId: string;
  eventType: string;
  eventData: Record<string, any>;
  timestamp: Date;
  source: 'app' | 'web' | 'api';
  sessionId?: string;
}

// Event type constants
export const USER_EVENTS = {
  PROFILE_UPDATED: 'profile_updated',
  DAILY_QUESTION_ANSWERED: 'daily_question_answered',
  MILESTONE_REACHED: 'milestone_reached',
  PARTNER_CONNECTED: 'partner_connected',
  SETTINGS_CHANGED: 'settings_changed',
  NOTIFICATION_SENT: 'notification_sent',
  APP_OPENED: 'app_opened',
  FEATURE_USED: 'feature_used',
  ONBOARDING_STARTED: 'onboarding_started',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  ONBOARDING_PARTNER_PROFILE_SET: 'onboarding_partner_profile_set',
  ONBOARDING_PARTNER_INVITED: 'onboarding_partner_invited',
  ONBOARDING_RITUAL_CONFIGURED: 'onboarding_ritual_configured',
  ONBOARDING_JOURNAL_ICON_SELECTED: 'onboarding_journal_icon_selected',
} as const;

export interface EventFilter {
  eventTypes?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  userId?: string;
  limit?: number;
  offset?: number;
}

export interface UserEventsState {
  events: UserEvent[];
  totalEvents: number;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
}

export function useUserEvents(initialFilter?: EventFilter) {
  const [state, setState] = useState<UserEventsState>({
    events: [],
    totalEvents: 0,
    isLoading: true,
    error: null,
    hasMore: false
  });

  const [filter, setFilter] = useState<EventFilter>(initialFilter || {});

  // Load events on mount and when filter changes
  useEffect(() => {
    loadEvents();
  }, [filter]);

  const loadEvents = async (append: boolean = false) => {
    try {
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      // Generate sample events
      const sampleEvents: UserEvent[] = Array.from({ length: filter.limit || 20 }, (_, i) => ({
        id: `event_${Date.now()}_${i}`,
        userId: filter.userId || 'current_user_id',
        eventType: [
          'profile_updated',
          'daily_question_answered',
          'achievement_unlocked',
          'partner_connected',
          'settings_changed',
          'module_completed',
          'streak_milestone',
          'date_night_planned'
        ][i % 8],
        eventData: {
          details: `Event ${i + 1} details`,
          value: Math.floor(Math.random() * 100),
          category: ['user', 'system', 'achievement'][i % 3]
        },
        timestamp: new Date(Date.now() - (i * 60 * 60 * 1000)), // Hours ago
        source: ['app', 'web', 'api'][i % 3] as 'app' | 'web' | 'api',
        sessionId: `session_${Math.random().toString(36).substring(7)}`
      }));

      // Apply filters
      let filteredEvents = sampleEvents;

      if (filter.eventTypes && filter.eventTypes.length > 0) {
        filteredEvents = filteredEvents.filter(event =>
          filter.eventTypes!.includes(event.eventType)
        );
      }

      if (filter.dateRange) {
        filteredEvents = filteredEvents.filter(event =>
          event.timestamp >= filter.dateRange!.start &&
          event.timestamp <= filter.dateRange!.end
        );
      }

      const totalEvents = filteredEvents.length + Math.floor(Math.random() * 100);
      const hasMore = filteredEvents.length >= (filter.limit || 20);

      setState(prev => ({
        ...prev,
        events: append ? [...prev.events, ...filteredEvents] : filteredEvents,
        totalEvents,
        hasMore,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load events',
        isLoading: false
      }));
    }
  };

  const trackEvent = async (eventType: string, eventData: Record<string, any> = {}): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 100));

      const newEvent: UserEvent = {
        id: `event_${Date.now()}`,
        userId: filter.userId || 'current_user_id',
        eventType,
        eventData,
        timestamp: new Date(),
        source: 'app',
        sessionId: `session_${Math.random().toString(36).substring(7)}`
      };

      setState(prev => ({
        ...prev,
        events: [newEvent, ...prev.events],
        totalEvents: prev.totalEvents + 1
      }));

      return true;
    } catch (error) {
      console.error('Failed to track event:', error);
      return false;
    }
  };

  const loadMore = async () => {
    if (!state.hasMore || state.isLoading) return;

    const newFilter = {
      ...filter,
      offset: (filter.offset || 0) + (filter.limit || 20)
    };

    setFilter(newFilter);
    await loadEvents(true);
  };

  const updateFilter = (newFilter: Partial<EventFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter, offset: 0 }));
  };

  const clearEvents = () => {
    setState(prev => ({
      ...prev,
      events: [],
      totalEvents: 0,
      hasMore: false
    }));
  };

  const getEventsByType = (eventType: string): UserEvent[] => {
    return state.events.filter(event => event.eventType === eventType);
  };

  const getEventsInDateRange = (start: Date, end: Date): UserEvent[] => {
    return state.events.filter(event =>
      event.timestamp >= start && event.timestamp <= end
    );
  };

  const getEventStats = () => {
    const eventTypeStats = state.events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const sourceStats = state.events.reduce((acc, event) => {
      acc[event.source] = (acc[event.source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const today = new Date();
    const todayEvents = state.events.filter(event =>
      event.timestamp.toDateString() === today.toDateString()
    ).length;

    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const weekEvents = state.events.filter(event =>
      event.timestamp >= thisWeek
    ).length;

    return {
      total: state.totalEvents,
      loaded: state.events.length,
      today: todayEvents,
      thisWeek: weekEvents,
      byType: eventTypeStats,
      bySource: sourceStats
    };
  };

  const exportEvents = (format: 'json' | 'csv' = 'json') => {
    if (format === 'json') {
      return JSON.stringify(state.events, null, 2);
    }

    // CSV format
    const headers = ['id', 'userId', 'eventType', 'timestamp', 'source'];
    const csvRows = [
      headers.join(','),
      ...state.events.map(event => [
        event.id,
        event.userId,
        event.eventType,
        event.timestamp.toISOString(),
        event.source
      ].join(','))
    ];

    return csvRows.join('\n');
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return {
    ...state,
    filter,
    trackEvent,
    logEvent: trackEvent, // Alias for backward compatibility
    loadMore,
    updateFilter,
    clearEvents,
    getEventsByType,
    getEventsInDateRange,
    getEventStats,
    exportEvents,
    clearError,
    refresh: () => loadEvents()
  };
}
