/**
 * Week One Data Hook
 * Manages data for the first week of the couple's journey
 */

import { useEffect, useState } from 'react';

export interface WeekOneActivity {
  id: string;
  title: string;
  description: string;
  type: 'question' | 'challenge' | 'reflection' | 'milestone';
  day: number; // 1-7
  isCompleted: boolean;
  completedAt?: Date;
  userResponse?: string;
  partnerResponse?: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface WeekOneProgress {
  totalActivities: number;
  completedActivities: number;
  currentDay: number;
  totalPoints: number;
  earnedPoints: number;
  completionPercentage: number;
  streak: number;
  isWeekComplete: boolean;
}

export interface WeekOneState {
  activities: WeekOneActivity[];
  progress: WeekOneProgress;
  currentActivity: WeekOneActivity | null;
  isLoading: boolean;
  error: string | null;
  // Additional properties for backward compatibility
  completedSections?: string[];
  dateNightPlan?: any;
  matchGameResponses?: any;
  chatPrompts?: any[];
  softStartupPractice?: any;
}

const sampleActivities: WeekOneActivity[] = [
  {
    id: 'w1_d1_q1',
    title: 'Getting to Know Each Other',
    description: 'Share three things about yourself that your partner might not know yet.',
    type: 'question',
    day: 1,
    isCompleted: false,
    points: 10,
    difficulty: 'easy'
  },
  {
    id: 'w1_d1_c1',
    title: 'Photo Challenge',
    description: 'Take a selfie together and share what you love about this moment.',
    type: 'challenge',
    day: 1,
    isCompleted: false,
    points: 15,
    difficulty: 'easy'
  },
  {
    id: 'w1_d2_q1',
    title: 'Dreams and Aspirations',
    description: 'What is one dream you have for your future together?',
    type: 'question',
    day: 2,
    isCompleted: false,
    points: 15,
    difficulty: 'medium'
  },
  {
    id: 'w1_d2_r1',
    title: 'Daily Reflection',
    description: 'Reflect on what made you smile today and share it with your partner.',
    type: 'reflection',
    day: 2,
    isCompleted: false,
    points: 10,
    difficulty: 'easy'
  },
  {
    id: 'w1_d3_q1',
    title: 'Favorite Memories',
    description: 'Share your favorite memory from before you met your partner.',
    type: 'question',
    day: 3,
    isCompleted: false,
    points: 15,
    difficulty: 'medium'
  },
  {
    id: 'w1_d4_c1',
    title: 'Gratitude Challenge',
    description: 'Write down three things you\'re grateful for about your partner.',
    type: 'challenge',
    day: 4,
    isCompleted: false,
    points: 20,
    difficulty: 'medium'
  },
  {
    id: 'w1_d5_q1',
    title: 'Future Plans',
    description: 'If you could plan the perfect weekend together, what would it include?',
    type: 'question',
    day: 5,
    isCompleted: false,
    points: 15,
    difficulty: 'medium'
  },
  {
    id: 'w1_d6_r1',
    title: 'Weekly Reflection',
    description: 'What has been the highlight of your week together?',
    type: 'reflection',
    day: 6,
    isCompleted: false,
    points: 20,
    difficulty: 'medium'
  },
  {
    id: 'w1_d7_m1',
    title: 'Week One Milestone',
    description: 'Celebrate completing your first week! Plan something special together.',
    type: 'milestone',
    day: 7,
    isCompleted: false,
    points: 30,
    difficulty: 'hard'
  }
];

export function useWeekOneData() {
  const [state, setState] = useState<WeekOneState>({
    activities: [],
    progress: {
      totalActivities: 0,
      completedActivities: 0,
      currentDay: 1,
      totalPoints: 0,
      earnedPoints: 0,
      completionPercentage: 0,
      streak: 0,
      isWeekComplete: false
    },
    currentActivity: null,
    isLoading: true,
    error: null
  });

  // Load week one data on mount
  useEffect(() => {
    loadWeekOneData();
  }, []);

  const loadWeekOneData = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 400));

      // Load saved progress from storage
      const savedProgress = localStorage.getItem('week_one_progress');
      let activities = [...sampleActivities];

      if (savedProgress) {
        const progress = JSON.parse(savedProgress);
        activities = activities.map(activity => ({
          ...activity,
          isCompleted: progress.completedActivities?.includes(activity.id) || false,
          completedAt: progress.completedActivities?.includes(activity.id)
            ? new Date(progress.completedAt || Date.now())
            : undefined,
          userResponse: progress.responses?.[activity.id]?.user,
          partnerResponse: progress.responses?.[activity.id]?.partner
        }));
      }

      const progress = calculateProgress(activities);
      const currentActivity = getCurrentActivity(activities);

      setState(prev => ({
        ...prev,
        activities,
        progress,
        currentActivity,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load week one data',
        isLoading: false
      }));
    }
  };

  const calculateProgress = (activities: WeekOneActivity[]): WeekOneProgress => {
    const totalActivities = activities.length;
    const completedActivities = activities.filter(a => a.isCompleted).length;
    const totalPoints = activities.reduce((sum, a) => sum + a.points, 0);
    const earnedPoints = activities.filter(a => a.isCompleted).reduce((sum, a) => sum + a.points, 0);
    const completionPercentage = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

    // Calculate current day based on completed activities
    const currentDay = Math.min(7, Math.floor(completedActivities / (totalActivities / 7)) + 1);

    // Calculate streak (consecutive days with completed activities)
    let streak = 0;
    for (let day = 1; day <= 7; day++) {
      const dayActivities = activities.filter(a => a.day === day);
      const dayCompleted = dayActivities.some(a => a.isCompleted);
      if (dayCompleted) {
        streak++;
      } else {
        break;
      }
    }

    const isWeekComplete = completedActivities === totalActivities;

    return {
      totalActivities,
      completedActivities,
      currentDay,
      totalPoints,
      earnedPoints,
      completionPercentage,
      streak,
      isWeekComplete
    };
  };

  const getCurrentActivity = (activities: WeekOneActivity[]): WeekOneActivity | null => {
    // Find the first incomplete activity
    return activities.find(activity => !activity.isCompleted) || null;
  };

  const completeActivity = async (activityId: string, userResponse?: string, partnerResponse?: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      const updatedActivities = state.activities.map(activity =>
        activity.id === activityId
          ? {
              ...activity,
              isCompleted: true,
              completedAt: new Date(),
              userResponse,
              partnerResponse
            }
          : activity
      );

      const progress = calculateProgress(updatedActivities);
      const currentActivity = getCurrentActivity(updatedActivities);

      // Save progress to storage
      const progressData = {
        completedActivities: updatedActivities.filter(a => a.isCompleted).map(a => a.id),
        completedAt: new Date().toISOString(),
        responses: updatedActivities.reduce((acc, activity) => {
          if (activity.userResponse || activity.partnerResponse) {
            acc[activity.id] = {
              user: activity.userResponse,
              partner: activity.partnerResponse
            };
          }
          return acc;
        }, {} as Record<string, { user?: string; partner?: string }>)
      };
      localStorage.setItem('week_one_progress', JSON.stringify(progressData));

      setState(prev => ({
        ...prev,
        activities: updatedActivities,
        progress,
        currentActivity,
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to complete activity',
        isLoading: false
      }));
      return false;
    }
  };

  const getActivitiesByDay = (day: number): WeekOneActivity[] => {
    return state.activities.filter(activity => activity.day === day);
  };

  const getActivitiesByType = (type: WeekOneActivity['type']): WeekOneActivity[] => {
    return state.activities.filter(activity => activity.type === type);
  };

  const resetWeek = async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Clear saved progress
      localStorage.removeItem('week_one_progress');

      // Reset all activities
      const resetActivities = sampleActivities.map(activity => ({
        ...activity,
        isCompleted: false,
        completedAt: undefined,
        userResponse: undefined,
        partnerResponse: undefined
      }));

      const progress = calculateProgress(resetActivities);
      const currentActivity = getCurrentActivity(resetActivities);

      setState(prev => ({
        ...prev,
        activities: resetActivities,
        progress,
        currentActivity,
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to reset week',
        isLoading: false
      }));
      return false;
    }
  };

  const getWeekSummary = () => {
    const { activities, progress } = state;

    return {
      ...progress,
      activitiesByType: {
        questions: activities.filter(a => a.type === 'question').length,
        challenges: activities.filter(a => a.type === 'challenge').length,
        reflections: activities.filter(a => a.type === 'reflection').length,
        milestones: activities.filter(a => a.type === 'milestone').length
      },
      completedByType: {
        questions: activities.filter(a => a.type === 'question' && a.isCompleted).length,
        challenges: activities.filter(a => a.type === 'challenge' && a.isCompleted).length,
        reflections: activities.filter(a => a.type === 'reflection' && a.isCompleted).length,
        milestones: activities.filter(a => a.type === 'milestone' && a.isCompleted).length
      },
      averagePointsPerActivity: progress.totalPoints / progress.totalActivities,
      daysActive: new Set(activities.filter(a => a.isCompleted).map(a => a.day)).size
    };
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Additional methods for backward compatibility
  const updateMatchGameResponse = (response: any) => {
    setState(prev => ({ ...prev, matchGameResponses: response }));
  };

  const updateDateNightPlan = (plan: any) => {
    setState(prev => ({ ...prev, dateNightPlan: plan }));
  };

  const updateChatPrompt = (prompt: any) => {
    setState(prev => ({
      ...prev,
      chatPrompts: prev.chatPrompts ? [...prev.chatPrompts, prompt] : [prompt]
    }));
  };

  const updateSoftStartupPractice = (practice: any) => {
    setState(prev => ({ ...prev, softStartupPractice: practice }));
  };

  const updateCompletedSections = (sections: string[]) => {
    setState(prev => ({ ...prev, completedSections: sections }));
  };

  return {
    ...state,
    data: state, // Alias for backward compatibility - return full state
    completeActivity,
    getActivitiesByDay,
    getActivitiesByType,
    resetWeek,
    getWeekSummary,
    clearError,
    refresh: loadWeekOneData,
    // Additional methods for backward compatibility
    updateMatchGameResponse,
    updateDateNightPlan,
    updateChatPrompt,
    updateSoftStartupPractice,
    updateCompletedSections
  };
}
