/**
 * Simplified Color System
 *
 * Clean, maintainable color system that replaces the previous 457-line
 * color file. Focuses on the core brand colors with proper theme support.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

// =============================================================================
// BRAND COLORS (Theme-Independent) - HTML Mockup Colors
// =============================================================================

export const BRAND_COLORS = {
  // Core Brand Palette - Exact HTML Mockup CSS Variables
  sageGreen: '#9CAF88',      // --sage-green: Primary brand color
  lavender: '#CBC3E3',       // --lavender: Secondary brand color
  accentPink: '#F3E0DA',     // --accent-pink: Accent color for highlights
  darkerPink: '#F7D0D6',     // --darker-pink: Darker accent for hover states
  charcoal: '#393939',       // --charcoal: Primary text color
  white: '#FFFFFF',          // --white: Pure white
  lightGray: '#F8F9FA',      // --light-gray: Light background

  // Legacy aliases for backward compatibility
  greenSage: '#9CAF88',      // Alias for sageGreen
  lavenderPurple: '#CBC3E3', // Alias for lavender
  charcoalGray: '#393939',   // Alias for charcoal

  // Base Colors
  black: '#000000',

  // Semantic Colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
} as const;

// =============================================================================
// LIGHT THEME
// =============================================================================

export const lightTheme = {
  // Brand Colors - HTML Mockup Aligned
  primary: BRAND_COLORS.sageGreen,
  secondary: BRAND_COLORS.lavender,
  accent: BRAND_COLORS.accentPink,
  accentDark: BRAND_COLORS.darkerPink,

  // Text Colors - HTML Mockup Aligned
  textPrimary: BRAND_COLORS.charcoal,
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: BRAND_COLORS.white,
  text: BRAND_COLORS.charcoal, // Legacy alias

  // Background Colors - White Dominant as per HTML Mockup
  background: BRAND_COLORS.white,        // Primary background now white
  backgroundSecondary: BRAND_COLORS.lightGray, // Light gray for secondary areas
  backgroundTertiary: BRAND_COLORS.accentPink,
  surface: BRAND_COLORS.white,           // Cards and surfaces are white

  // Border Colors - Subtle as per HTML Mockup
  border: 'rgba(0, 0, 0, 0.05)',        // Very subtle borders
  borderLight: '#E5E7EB',
  borderMedium: '#D1D5DB',
  borderDark: '#9CA3AF',

  // Semantic Colors
  success: BRAND_COLORS.success,
  warning: BRAND_COLORS.warning,
  error: BRAND_COLORS.error,
  info: BRAND_COLORS.info,

  // Interactive States - Updated for new primary colors
  primaryLight: '#B4C49A',               // Lighter sage green
  primaryDark: '#7A9470',                // Darker sage green
  secondaryLight: '#D8D0E8',             // Lighter lavender
  secondaryDark: '#B8ACDB',              // Darker lavender

  // Shadow & Overlay - HTML Mockup Shadow Style
  shadow: BRAND_COLORS.black,
  shadowLight: 'rgba(0, 0, 0, 0.1)',     // HTML mockup shadow: 0 8px 32px rgba(0,0,0,0.1)
  shadowMedium: 'rgba(0, 0, 0, 0.15)',
  shadowDark: 'rgba(0, 0, 0, 0.25)',
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',

  // Glass Effect - HTML Mockup Glass-morphism
  glass: 'rgba(255, 255, 255, 0.8)',     // HTML mockup: background: rgba(255, 255, 255, 0.8)
  glassBorder: 'rgba(0, 0, 0, 0.05)',    // Subtle border for glass elements
} as const;

// =============================================================================
// DARK THEME
// =============================================================================

export const darkTheme = {
  // Brand Colors (adapted for dark mode)
  primary: '#A8C094',        // Lighter green sage
  secondary: '#D4CCEB',      // Lighter lavender purple
  accent: '#F5E6DC',         // Lighter accent pink
  accentDark: '#F9D6DC',     // Lighter darker pink

  // Text Colors (inverted)
  textPrimary: '#E5E5E5',    // Light text
  textSecondary: '#B0B0B0',  // Medium gray text
  textTertiary: '#808080',   // Darker gray text
  textInverse: '#1A1A1A',    // Dark text for light elements
  text: '#E5E5E5',           // Legacy alias

  // Background Colors (dark variants)
  background: '#1A1A1A',           // Dark primary background
  backgroundSecondary: '#2A2A2A',  // Slightly lighter for cards
  backgroundTertiary: '#3A2F2A',   // Dark variant of accent
  surface: '#2A2A2A',              // Dark surface

  // Border Colors (adapted)
  border: '#3A3A3A',         // Dark border
  borderLight: '#4A4A4A',    // Medium dark border
  borderMedium: '#5A5A5A',   // Lighter dark border
  borderDark: '#6A6A6A',     // Lightest dark border

  // Semantic Colors (adapted for dark backgrounds)
  success: '#20C991',
  warning: '#FFD700',
  error: '#FF6B6B',
  info: '#70B5FF',

  // Interactive States (adapted)
  primaryLight: '#B8D0A4',
  primaryDark: '#98B084',
  secondaryLight: '#E0D8F0',
  secondaryDark: '#C8C0E6',

  // Shadow & Overlay (adapted)
  shadow: BRAND_COLORS.black,
  shadowLight: 'rgba(0, 0, 0, 0.3)',
  shadowMedium: 'rgba(0, 0, 0, 0.5)',
  shadowDark: 'rgba(0, 0, 0, 0.7)',
  overlay: 'rgba(0, 0, 0, 0.7)',
  overlayLight: 'rgba(0, 0, 0, 0.5)',
  overlayDark: 'rgba(0, 0, 0, 0.9)',

  // Glass Effect (adapted)
  glass: 'rgba(42, 42, 42, 0.8)',
  glassBorder: 'rgba(255, 255, 255, 0.1)',
} as const;

// =============================================================================
// THEME TYPES & UTILITIES
// =============================================================================

export type ThemeMode = 'light' | 'dark' | 'auto';
export type Theme = typeof lightTheme;
export type ColorKey = keyof Theme;

/**
 * Get theme colors based on mode
 */
export const getThemeColors = (
  mode: ThemeMode,
  systemColorScheme?: 'light' | 'dark' | null
): Theme => {
  switch (mode) {
    case 'dark':
      return darkTheme as Theme;
    case 'light':
      return lightTheme;
    case 'auto':
      return (systemColorScheme === 'dark' ? darkTheme : lightTheme) as Theme;
    default:
      return lightTheme;
  }
};

/**
 * Get color with opacity
 */
export const getColorWithOpacity = (color: string, opacity: number): string => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Check if color is light or dark
 */
export const isLightColor = (color: string): boolean => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128;
};

// =============================================================================
// LEGACY SUPPORT
// =============================================================================

// Default export for backward compatibility (defaults to light theme)
export const colors = lightTheme;
export default colors;
