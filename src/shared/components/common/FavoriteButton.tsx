/**
 * Reusable Favorite Button Component
 * Provides consistent favorite functionality across all features
 */

import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Heart } from 'lucide-react-native';
import { colors } from '../../utils/colors';
import { useFavorites } from '../../hooks/useFavorites';
import { FavoriteItemType, FavoriteMetadata } from '../../services/favoritesService';
import { logger } from '../../utils/logger';

export interface FavoriteButtonProps {
  itemId: string;
  itemType: FavoriteItemType;
  sourceTable?: string;
  metadata?: FavoriteMetadata;
  size?: number;
  style?: ViewStyle;
  onToggle?: (isFavorited: boolean) => void;
  disabled?: boolean;
}

export const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  itemId,
  itemType,
  sourceTable,
  metadata,
  size = 24,
  style,
  onToggle,
  disabled = false
}) => {
  const { isFavorited, toggleFavorite } = useFavorites();
  const favorited = isFavorited(itemId, itemType);

  const handlePress = async () => {
    if (disabled) return;

    try {
      const success = await toggleFavorite(itemId, itemType, sourceTable, metadata);
      
      if (success) {
        const newFavoriteState = !favorited;
        onToggle?.(newFavoriteState);
        logger.info('Favorite toggled:', { itemId, itemType, favorited: newFavoriteState });
      } else {
        logger.warn('Failed to toggle favorite:', { itemId, itemType });
      }
    } catch (error) {
      logger.error('Error in favorite button:', error);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.button, style, disabled && styles.disabled]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Heart
        size={size}
        color={favorited ? colors.error : colors.textSecondary}
        fill={favorited ? colors.error : 'transparent'}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

export default FavoriteButton;
