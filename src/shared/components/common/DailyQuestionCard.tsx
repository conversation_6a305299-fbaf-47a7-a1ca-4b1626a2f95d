/**
 * Daily Question Card Component
 *
 * Displays today's daily question with response status and quick actions.
 * Integrates with the home page to provide easy access to daily bonding.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { router } from 'expo-router';
import { ArrowRight, CheckCircle, Clock, Heart, MessageCircle } from 'lucide-react-native';
import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDailyQuestions } from '../../../journeys/daily/useDailyQuestions';
import { tokens } from '../../utils/theme';
import { DSButton, DSCard } from './index';
import { useGlobalTheme } from './ThemeProvider';

export interface DailyQuestionCardProps {
  style?: any;
  onPress?: () => void;
}

export const DailyQuestionCard: React.FC<DailyQuestionCardProps> = ({
  style,
  onPress
}) => {
  const { currentTheme } = useGlobalTheme();

  // Critical safety check for theme properties
  const safeTheme = currentTheme || {
    textSecondary: '#6B7280',
    success: '#10B981',
    warning: '#F59E0B',
    primary: '#9CAF88',
    error: '#EF4444',
    textPrimary: '#393939',
    border: '#E5E7EB'
  };

  const {
    todaysQuestion,
    hasUserAnswered,
    hasPartnerAnswered,
    isLoading,
    error,
    streakData
  } = useDailyQuestions();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCardPress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push('/daily-questions');
    }
  };

  const getStatusText = () => {
    if (isLoading) return 'Loading...';
    if (error) return 'Unable to load question';
    if (!todaysQuestion) return 'No question for today';

    if (hasUserAnswered && hasPartnerAnswered) {
      return 'Both answered! View responses';
    } else if (hasUserAnswered) {
      return 'You answered! Waiting for partner';
    } else if (hasPartnerAnswered) {
      return 'Partner answered! Your turn';
    } else {
      return 'Answer today\'s question';
    }
  };

  const getStatusColor = () => {
    if (isLoading || error) return safeTheme.textSecondary;
    if (hasUserAnswered && hasPartnerAnswered) return safeTheme.success;
    if (hasUserAnswered || hasPartnerAnswered) return safeTheme.warning;
    return safeTheme.primary;
  };

  const getStatusIcon = () => {
    if (isLoading) return <ActivityIndicator size="small" color={safeTheme.textSecondary} />;
    if (error) return <MessageCircle size={16} color={safeTheme.textSecondary} />;
    if (hasUserAnswered && hasPartnerAnswered) return <CheckCircle size={16} color={safeTheme.success} />;
    if (hasUserAnswered || hasPartnerAnswered) return <Clock size={16} color={safeTheme.warning} />;
    return <MessageCircle size={16} color={safeTheme.primary} />;
  };

  const getCategoryEmoji = (category: string) => {
    const emojiMap: Record<string, string> = {
      deep: '💭',
      fun: '🎉',
      funny: '😄',
      memories: '📸',
      dreams: '✨',
      gratitude: '🙏'
    };
    return emojiMap[category] || '💬';
  };

  const getDifficultyColor = (difficulty: string) => {
    const colorMap: Record<string, string> = {
      easy: safeTheme.success,
      medium: safeTheme.warning,
      deep: safeTheme.error
    };
    return colorMap[difficulty] || safeTheme.textSecondary;
  };

  if (isLoading) {
    return (
      <DSCard style={StyleSheet.flatten([styles.card, style])}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={safeTheme.primary} />
          <Text style={[styles.loadingText, { color: safeTheme.textSecondary }]}>
            Loading today's question...
          </Text>
        </View>
      </DSCard>
    );
  }

  if (error || !todaysQuestion) {
    return (
      <DSCard style={StyleSheet.flatten([styles.card, style])}>
        <View style={styles.errorContainer}>
          <MessageCircle size={24} color={safeTheme.textSecondary} />
          <Text style={[styles.errorText, { color: safeTheme.textSecondary }]}>
            {error || 'No question available today'}
          </Text>
          <DSButton
            title="Try Again"
            onPress={() => window.location.reload()}
            variant="outline"
            size="sm"
          />
        </View>
      </DSCard>
    );
  }

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <DSCard style={StyleSheet.flatten([styles.card, style])}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <Text style={[styles.title, { color: safeTheme.textPrimary }]}>
              Daily Question
            </Text>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryEmoji}>
                {getCategoryEmoji(todaysQuestion.category)}
              </Text>
            </View>
          </View>

          {/* Streak indicator */}
          {streakData && streakData.current_streak > 0 && (
            <View style={styles.streakContainer}>
              <Heart size={12} color={safeTheme.error} fill={safeTheme.error} />
              <Text style={[styles.streakText, { color: safeTheme.error }]}>
                {streakData.current_streak} day streak
              </Text>
            </View>
          )}
        </View>

        {/* Question */}
        <Text style={[styles.questionText, { color: safeTheme.textPrimary }]} numberOfLines={3}>
          {todaysQuestion.question_text}
        </Text>

        {/* Status and Actions */}
        <View style={styles.footer}>
          <View style={styles.statusContainer}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>

          <View style={styles.actionContainer}>
            <View style={styles.difficultyIndicator}>
              <View
                style={[
                  styles.difficultyDot,
                  { backgroundColor: getDifficultyColor(todaysQuestion.difficulty) }
                ]}
              />
              <Text style={[styles.difficultyText, { color: safeTheme.textSecondary }]}>
                {todaysQuestion.difficulty}
              </Text>
            </View>

            <ArrowRight size={16} color={safeTheme.textSecondary} />
          </View>
        </View>

        {/* Progress indicators */}
        <View style={styles.progressContainer}>
          <View style={styles.progressRow}>
            <View style={[
              styles.progressDot,
              { backgroundColor: hasUserAnswered ? safeTheme.success : safeTheme.border }
            ]} />
            <Text style={[styles.progressLabel, { color: safeTheme.textSecondary }]}>
              You
            </Text>
          </View>

          <View style={styles.progressRow}>
            <View style={[
              styles.progressDot,
              { backgroundColor: hasPartnerAnswered ? safeTheme.success : safeTheme.border }
            ]} />
            <Text style={[styles.progressLabel, { color: safeTheme.textSecondary }]}>
              Partner
            </Text>
          </View>
        </View>
      </DSCard>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: tokens.spacing.lg,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing.xl,
  },
  loadingText: {
    marginTop: tokens.spacing.sm,
    fontSize: tokens.fontSizes.sm,
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing.lg,
  },
  errorText: {
    marginVertical: tokens.spacing.sm,
    fontSize: tokens.fontSizes.sm,
    textAlign: 'center',
  },
  header: {
    marginBottom: tokens.spacing.md,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing.xs,
  },
  title: {
    fontSize: tokens.fontSizes.lg,
    fontWeight: tokens.fontWeights.semibold,
  },
  categoryBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryEmoji: {
    fontSize: 16,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  streakText: {
    marginLeft: tokens.spacing.xs,
    fontSize: tokens.fontSizes.xs,
    fontWeight: tokens.fontWeights.medium,
  },
  questionText: {
    fontSize: tokens.fontSizes.md,
    lineHeight: tokens.lineHeights.normal,
    marginBottom: tokens.spacing.lg,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing.md,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusText: {
    marginLeft: tokens.spacing.sm,
    fontSize: tokens.fontSizes.sm,
    fontWeight: tokens.fontWeights.medium,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: tokens.spacing.sm,
  },
  difficultyDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: tokens.spacing.xs,
  },
  difficultyText: {
    fontSize: tokens.fontSizes.xs,
    textTransform: 'capitalize',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: tokens.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: tokens.spacing.xs,
  },
  progressLabel: {
    fontSize: tokens.fontSizes.xs,
  },
});
