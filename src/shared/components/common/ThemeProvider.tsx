/**
 * Global Theme Provider System
 *
 * Provides comprehensive theme support throughout the entire app.
 * Automatically applies dark/light mode to all components.
 * Integrated with SettingsContext for consistent theme management.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { createContext, ReactNode, useContext } from 'react';
import { StatusBar, View } from 'react-native';
import { useSettings } from '../../../contexts/SettingsContext';
import type { CompleteTheme, Theme, ThemeMode } from '../../utils/theme';
import { getCompleteTheme } from '../../utils/theme';

// Theme Context
interface ThemeContextType {
  currentTheme: Theme;
  theme: Theme; // Alias for backward compatibility
  isDarkMode: boolean;
  themeMode: ThemeMode;
  completeTheme: CompleteTheme;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Global Theme Provider Component
interface GlobalThemeProviderProps {
  children: ReactNode;
}

export const GlobalThemeProvider: React.FC<GlobalThemeProviderProps> = ({ children }) => {
  const { settings, isDarkMode, updateSetting, systemColorScheme } = useSettings();

  // Get complete theme with tokens - use the actual resolved theme mode
  const resolvedMode: ThemeMode = settings.theme === 'auto'
    ? (systemColorScheme === 'dark' ? 'dark' : 'light')
    : settings.theme;
  const completeTheme = getCompleteTheme(resolvedMode);

  const setThemeMode = async (mode: ThemeMode) => {
    await updateSetting('theme', mode);
  };

  const contextValue: ThemeContextType = {
    currentTheme: completeTheme,
    theme: {
      ...completeTheme,
      colors: {
        background: completeTheme.background,
        surface: completeTheme.backgroundSecondary,
        text: completeTheme.textPrimary,
        textSecondary: completeTheme.textSecondary,
        primary: completeTheme.primary,
        secondary: completeTheme.secondary,
        accent: completeTheme.accent,
        error: completeTheme.error || '#EF4444',
        warning: completeTheme.warning || '#F59E0B',
        success: completeTheme.success || '#10B981',
      }
    } as any, // Type assertion to handle the mixed structure
    isDarkMode,
    themeMode: settings.theme, // Keep the user's preference (including 'auto')
    completeTheme,
    setThemeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={completeTheme.background}
      />
      <View style={{ flex: 1, backgroundColor: completeTheme.background }}>
        {children}
      </View>
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useGlobalTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useGlobalTheme must be used within a GlobalThemeProvider');
  }
  return context;
};

// Enhanced hook for theme colors with full theme support
export const useThemeColors = () => {
  const { currentTheme, isDarkMode, completeTheme } = useGlobalTheme();

  return {
    // All theme colors
    ...currentTheme,
    // Convenience flag
    isDarkMode,
    // Complete theme with tokens
    tokens: completeTheme.tokens,
  };
};

// Hook for theme mode management
export const useThemeMode = () => {
  const { themeMode, setThemeMode, isDarkMode } = useGlobalTheme();

  return {
    mode: themeMode,
    setMode: setThemeMode,
    isDarkMode,
    isAutoMode: themeMode === 'auto',
    toggleMode: () => {
      // When toggling, cycle through light -> dark -> auto
      if (themeMode === 'light') {
        setThemeMode('dark');
      } else if (themeMode === 'dark') {
        setThemeMode('auto');
      } else {
        setThemeMode('light');
      }
    },
    setLightMode: () => setThemeMode('light'),
    setDarkMode: () => setThemeMode('dark'),
    setAutoMode: () => setThemeMode('auto'),
  };
};

export default GlobalThemeProvider;
