/**
 * Accessible UI Components
 * WCAG 2.1 AA compliant components with built-in accessibility features
 */

import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import {
    AccessibilityRole,
    Image,
    ImageProps,
    StyleSheet,
    Text,
    TextInput,
    TextInputProps,
    TextStyle,
    TouchableOpacity,
    TouchableOpacityProps,
    View,
    ViewStyle
} from 'react-native';
import { useAppTheme } from '../../hooks/useAppTheme';
import {
    announceToScreenReader,
    getAccessibilityProps
} from '../../services/system/accessibilityManager';

// =============================================================================
// ACCESSIBLE BUTTON
// =============================================================================

interface AccessibleButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  accessibilityHint?: string;
  accessibilityActions?: Array<{ name: string; label?: string }>;
  onAccessibilityAction?: (event: { nativeEvent: { actionName: string } }) => void;
}

export const AccessibleButton = forwardRef<typeof TouchableOpacity, AccessibleButtonProps>(
  ({
    title,
    variant = 'primary',
    size = 'medium',
    loading = false,
    disabled = false,
    accessibilityHint,
    accessibilityActions,
    onAccessibilityAction,
    style,
    onPress,
    ...props
  }, ref) => {
    const { theme } = useAppTheme();
    const buttonRef = useRef<typeof TouchableOpacity>(null);

    // Fallback theme if theme is undefined
    const safeTheme = theme || {
      primary: '#9CAF88',
      secondary: '#CBC3E3',
      textInverse: '#FFFFFF',
      border: '#E5E7EB'
    };

    useImperativeHandle(ref, () => buttonRef.current!);

    const handlePress = (event: any) => {
      if (loading || disabled) return;

      // Announce button press to screen reader
      announceToScreenReader(`${title} button pressed`);

      onPress?.(event);
    };

    const accessibilityProps = getAccessibilityProps({
      role: 'button' as AccessibilityRole,
      label: loading ? `${title}, loading` : title,
      hint: accessibilityHint || `Activates ${title}`,
      state: {
        disabled: disabled || loading,
        busy: loading
      },
      actions: accessibilityActions
    });

    const buttonStyles = [
      styles.button,
      (styles as any)[`button${size.charAt(0).toUpperCase() + size.slice(1)}`],
      (styles as any)[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
      {
        backgroundColor: getButtonBackgroundColor(variant, safeTheme),
        borderColor: getButtonBorderColor(variant, safeTheme),
        opacity: (disabled || loading) ? 0.6 : 1
      },
      style
    ];

    const textStyles = [
      styles.buttonText,
      (styles as any)[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}`],
      {
        color: getButtonTextColor(variant, safeTheme)
      }
    ];

    return (
      <TouchableOpacity
        ref={buttonRef}
        style={buttonStyles}
        onPress={handlePress}
        disabled={disabled || loading}
        onAccessibilityAction={onAccessibilityAction}
        {...accessibilityProps}
        {...props}
      >
        <Text style={textStyles}>
          {loading ? 'Loading...' : title}
        </Text>
      </TouchableOpacity>
    );
  }
);

// =============================================================================
// ACCESSIBLE TEXT INPUT
// =============================================================================

interface AccessibleTextInputProps extends TextInputProps {
  label: string;
  error?: string;
  required?: boolean;
  helpText?: string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
}

export const AccessibleTextInput = forwardRef<TextInput, AccessibleTextInputProps>(
  ({
    label,
    error,
    required = false,
    helpText,
    containerStyle,
    labelStyle,
    errorStyle,
    style,
    ...props
  }, ref) => {
    const { theme } = useAppTheme();
    const inputRef = useRef<TextInput>(null);

    useImperativeHandle(ref, () => inputRef.current!);

    // Fallback theme if theme is undefined
    const safeTheme = theme || {
      text: '#393939',
      error: '#EF4444',
      surface: '#FFFFFF',
      border: '#E5E7EB',
      textSecondary: '#6B7280'
    };

    const accessibilityLabel = required ? `${label}, required` : label;
    const accessibilityHint = error
      ? `Error: ${error}`
      : helpText
        ? helpText
        : `Enter ${label.toLowerCase()}`;

    const accessibilityProps = getAccessibilityProps({
      role: 'text' as AccessibilityRole,
      label: accessibilityLabel,
      hint: accessibilityHint,
      state: {
        disabled: props.editable === false
      }
    });

    return (
      <View style={[styles.inputContainer, containerStyle]}>
        <Text style={[styles.inputLabel, { color: safeTheme.text }, labelStyle]}>
          {label}
          {required && <Text style={[styles.required, { color: safeTheme.error }]}> *</Text>}
        </Text>

        <TextInput
          ref={inputRef}
          style={[
            styles.textInput,
            {
              backgroundColor: safeTheme.surface,
              borderColor: error ? safeTheme.error : safeTheme.border,
              color: safeTheme.text
            },
            style
          ]}
          placeholderTextColor={safeTheme.textSecondary}
          {...accessibilityProps}
          {...props}
        />

        {error && (
          <Text
            style={[styles.errorText, { color: safeTheme.error }, errorStyle]}
            {...getAccessibilityProps({
              role: 'alert' as AccessibilityRole,
              label: `Error: ${error}`,
              liveRegion: 'assertive'
            })}
          >
            {error}
          </Text>
        )}

        {helpText && !error && (
          <Text style={[styles.helpText, { color: safeTheme.textSecondary }]}>
            {helpText}
          </Text>
        )}
      </View>
    );
  }
);

// =============================================================================
// ACCESSIBLE IMAGE
// =============================================================================

interface AccessibleImageProps extends ImageProps {
  alt: string;
  decorative?: boolean;
  caption?: string;
}

export const AccessibleImage: React.FC<AccessibleImageProps> = ({
  alt,
  decorative = false,
  caption,
  style,
  ...props
}) => {
  const { theme } = useAppTheme();

  // Fallback theme if theme is undefined
  const safeTheme = theme || {
    textSecondary: '#6B7280'
  };

  const accessibilityProps = decorative
    ? {
        accessible: false,
        accessibilityElementsHidden: true
      }
    : getAccessibilityProps({
        role: 'image' as AccessibilityRole,
        label: alt,
        hint: caption ? `Image caption: ${caption}` : undefined
      });

  return (
    <View style={styles.imageContainer}>
      <Image
        style={[styles.image, style]}
        {...accessibilityProps}
        {...props}
      />
      {caption && (
        <Text
          style={[styles.imageCaption, { color: safeTheme.textSecondary }]}
          {...getAccessibilityProps({
            role: 'text' as AccessibilityRole,
            label: `Image caption: ${caption}`
          })}
        >
          {caption}
        </Text>
      )}
    </View>
  );
};

// =============================================================================
// ACCESSIBLE HEADING
// =============================================================================

interface AccessibleHeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: string;
  style?: TextStyle;
}

export const AccessibleHeading: React.FC<AccessibleHeadingProps> = ({
  level,
  children,
  style
}) => {
  const { theme } = useAppTheme();

  // Fallback theme if theme is undefined
  const safeTheme = theme || {
    text: '#393939'
  };

  const accessibilityProps = getAccessibilityProps({
    role: 'header' as AccessibilityRole,
    label: `Heading level ${level}: ${children}`
  });

  const headingStyle = [
    styles.heading,
    styles[`heading${level}` as keyof typeof styles],
    { color: safeTheme.text },
    style
  ];

  return (
    <Text style={headingStyle} {...accessibilityProps}>
      {children}
    </Text>
  );
};

// =============================================================================
// ACCESSIBLE LIST
// =============================================================================

interface AccessibleListProps {
  data: Array<{ id: string; content: React.ReactNode }>;
  title?: string;
  style?: ViewStyle;
  itemStyle?: ViewStyle;
}

export const AccessibleList: React.FC<AccessibleListProps> = ({
  data,
  title,
  style,
  itemStyle
}) => {
  const { theme } = useAppTheme();

  // Fallback theme if theme is undefined
  const safeTheme = theme || {};

  const listProps = getAccessibilityProps({
    role: 'list' as AccessibilityRole,
    label: title ? `${title} list with ${data.length} items` : `List with ${data.length} items`
  });

  return (
    <View style={[styles.list, style]} {...listProps}>
      {title && (
        <AccessibleHeading level={2} style={styles.listTitle}>
          {title}
        </AccessibleHeading>
      )}
      {data.map((item, index) => (
        <View
          key={item.id}
          style={[styles.listItem, { borderBottomColor: theme.border }, itemStyle]}
          {...getAccessibilityProps({
            role: 'text' as AccessibilityRole,
            label: `List item ${index + 1} of ${data.length}`
          })}
        >
          {item.content}
        </View>
      ))}
    </View>
  );
};

// =============================================================================
// ACCESSIBLE MODAL
// =============================================================================

interface AccessibleModalProps {
  visible: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
  style?: ViewStyle;
}

export const AccessibleModal: React.FC<AccessibleModalProps> = ({
  visible,
  title,
  children,
  onClose,
  style
}) => {
  const { theme } = useAppTheme();

  // Fallback theme if theme is undefined
  const safeTheme = theme || {};

  if (!visible) return null;

  const modalProps = getAccessibilityProps({
    role: 'alert' as AccessibilityRole,
    label: `${title} dialog`,
    hint: 'Modal dialog is open'
  });

  return (
    <View style={styles.modalOverlay}>
      <View
        style={[
          styles.modalContent,
          { backgroundColor: theme.surface, borderColor: theme.border },
          style
        ]}
        {...modalProps}
      >
        <View style={styles.modalHeader}>
          <AccessibleHeading level={1} style={styles.modalTitle}>
            {title}
          </AccessibleHeading>
          <AccessibleButton
            title="Close"
            variant="ghost"
            size="small"
            onPress={onClose}
            accessibilityHint="Closes the dialog"
          />
        </View>
        <View style={styles.modalBody}>
          {children}
        </View>
      </View>
    </View>
  );
};

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

const getButtonBackgroundColor = (variant: string, theme: any) => {
  const safeTheme = theme || { primary: '#9CAF88', secondary: '#CBC3E3' };
  switch (variant) {
    case 'primary': return safeTheme.primary;
    case 'secondary': return safeTheme.secondary;
    case 'outline': return 'transparent';
    case 'ghost': return 'transparent';
    default: return safeTheme.primary;
  }
};

const getButtonBorderColor = (variant: string, theme: any) => {
  const safeTheme = theme || { primary: '#9CAF88' };
  switch (variant) {
    case 'outline': return safeTheme.primary;
    case 'ghost': return 'transparent';
    default: return 'transparent';
  }
};

const getButtonTextColor = (variant: string, theme: any) => {
  const safeTheme = theme || { textInverse: '#FFFFFF', primary: '#9CAF88' };
  switch (variant) {
    case 'primary': return safeTheme.textInverse;
    case 'secondary': return safeTheme.textInverse;
    case 'outline': return safeTheme.primary;
    case 'ghost': return safeTheme.primary;
    default: return safeTheme.textInverse;
  }
};

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  // Button styles
  button: {
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44, // Minimum touch target size
    minWidth: 44
  },
  buttonSmall: {
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  buttonMedium: {
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  buttonLarge: {
    paddingHorizontal: 20,
    paddingVertical: 16
  },
  buttonPrimary: {},
  buttonSecondary: {},
  buttonOutline: {},
  buttonGhost: {},
  buttonText: {
    fontWeight: '600',
    textAlign: 'center'
  },
  buttonTextSmall: {
    fontSize: 14
  },
  buttonTextMedium: {
    fontSize: 16
  },
  buttonTextLarge: {
    fontSize: 18
  },

  // Input styles
  inputContainer: {
    marginBottom: 16
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8
  },
  required: {
    fontSize: 16,
    fontWeight: '600'
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 44
  },
  errorText: {
    fontSize: 14,
    marginTop: 4
  },
  helpText: {
    fontSize: 14,
    marginTop: 4
  },

  // Image styles
  imageContainer: {
    alignItems: 'center'
  },
  image: {
    borderRadius: 8
  },
  imageCaption: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    fontStyle: 'italic'
  },

  // Heading styles
  heading: {
    fontWeight: 'bold',
    marginVertical: 8
  },
  heading1: {
    fontSize: 32
  },
  heading2: {
    fontSize: 28
  },
  heading3: {
    fontSize: 24
  },
  heading4: {
    fontSize: 20
  },
  heading5: {
    fontSize: 18
  },
  heading6: {
    fontSize: 16
  },

  // List styles
  list: {
    marginVertical: 8
  },
  listTitle: {
    marginBottom: 12
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    minHeight: 44
  },

  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    borderWidth: 1,
    maxHeight: '80%'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0'
  },
  modalTitle: {
    flex: 1,
    marginRight: 16
  },
  modalBody: {
    padding: 16
  }
});
