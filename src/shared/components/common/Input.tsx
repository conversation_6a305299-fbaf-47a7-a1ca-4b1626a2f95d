/**
 * Input Component
 *
 * Unified input component that provides consistent styling and behavior
 * across the application. Replaces scattered input implementations.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React, { useState } from 'react';
import {
    Text,
    TextInput,
    TextInputProps,
    TextStyle,
    View,
    ViewStyle
} from 'react-native';
import { getTextStyle, tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

export interface InputProps extends Omit<TextInputProps, 'style'> {
  // Content
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;

  // Validation
  error?: string;
  required?: boolean;

  // Appearance
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  multiline?: boolean;

  // Icons
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;

  // Styling
  style?: ViewStyle;
  inputStyle?: TextStyle;

  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
}

// =============================================================================
// COMPONENT
// =============================================================================

// Internal component that requires theme prop
const InputInternal: React.FC<InputProps & { theme: Theme }> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  required = false,
  variant = 'default',
  size = 'md',
  multiline = false,
  leftIcon,
  rightIcon,
  style,
  inputStyle,
  accessibilityLabel,
  accessibilityHint,
  testID,
  theme,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  // Get styles based on variant and state
  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: tokens.radii.sm,
      minHeight: tokens.sizes.input[size],
      paddingHorizontal: tokens.spacing.md,
      paddingVertical: tokens.spacing.sm,
      flexDirection: 'row',
      alignItems: multiline ? 'flex-start' : 'center',
    };

    const variantStyles = {
      default: {
        backgroundColor: theme.backgroundSecondary,
        borderWidth: tokens.borders.thin,
        borderColor: error ? theme.error : isFocused ? theme.primary : theme.border,
      },
      outlined: {
        backgroundColor: 'transparent',
        borderWidth: tokens.borders.thick,
        borderColor: error ? theme.error : isFocused ? theme.primary : theme.border,
      },
      filled: {
        backgroundColor: theme.backgroundTertiary,
        borderWidth: 0,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  const getInputStyle = (): TextStyle => {
    const baseTextStyle = getTextStyle('body', theme);

    return {
      ...baseTextStyle,
      flex: 1,
      color: theme.textPrimary,
      fontSize: tokens.typography.fontSize.md,
      ...(multiline && {
        minHeight: tokens.sizes.input.lg,
        textAlignVertical: 'top',
      }),
      ...inputStyle,
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      ...getTextStyle('body', theme),
      fontSize: tokens.typography.fontSize.sm,
      fontWeight: tokens.typography.fontWeight.medium,
      color: theme.textSecondary,
      marginBottom: tokens.spacing.xs,
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      ...getTextStyle('caption', theme),
      color: theme.error,
      marginTop: tokens.spacing.xs,
    };
  };

  return (
    <View style={style}>
      {/* Label */}
      {label && (
        <Text style={getLabelStyle()}>
          {label}
          {required && <Text style={{ color: theme.error }}> *</Text>}
        </Text>
      )}

      {/* Input Container */}
      <View style={getContainerStyle()}>
        {/* Left Icon */}
        {leftIcon && (
          <View style={{ marginRight: tokens.spacing.sm }}>
            {leftIcon}
          </View>
        )}

        {/* Text Input */}
        <TextInput
          {...textInputProps}
          style={getInputStyle()}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.textTertiary}
          multiline={multiline}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          accessibilityLabel={accessibilityLabel || label}
          accessibilityHint={accessibilityHint}
          testID={testID}
        />

        {/* Right Icon */}
        {rightIcon && (
          <View style={{ marginLeft: tokens.spacing.sm }}>
            {rightIcon}
          </View>
        )}
      </View>

      {/* Error Message */}
      {error && (
        <Text style={getErrorStyle()}>
          {error}
        </Text>
      )}
    </View>
  );
};

// =============================================================================
// THEME-AWARE WRAPPER
// =============================================================================

import { useGlobalTheme } from './ThemeProvider';

/**
 * Input component with automatic theme injection
 */
export const Input: React.FC<InputProps> = (props) => {
  const { currentTheme } = useGlobalTheme();
  return <InputInternal {...props} theme={currentTheme} />;
};

// =============================================================================
// VARIANTS
// =============================================================================

/**
 * Outlined Input - With border
 */
export const OutlinedInput: React.FC<Omit<InputProps, 'variant'>> = (props) => (
  <Input {...props} variant="outlined" />
);

/**
 * Filled Input - With background
 */
export const FilledInput: React.FC<Omit<InputProps, 'variant'>> = (props) => (
  <Input {...props} variant="filled" />
);

/**
 * Textarea - Multiline input
 */
export const Textarea: React.FC<Omit<InputProps, 'multiline'>> = (props) => (
  <Input {...props} multiline size="lg" />
);

// =============================================================================
// LEGACY SUPPORT
// =============================================================================

/**
 * @deprecated Use Input component instead
 */
export const AuthInput = Input;

export default Input;
