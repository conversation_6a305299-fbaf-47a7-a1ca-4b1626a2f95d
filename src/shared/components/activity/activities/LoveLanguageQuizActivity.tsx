/**
 * Love Language Quiz Activity Component
 * Example implementation of a modular activity component
 */

import { ArrowR<PERSON>, Gift, Heart, MessageCircle, Star, Users } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import {
    ActivityError,
    ActivityProps,
    ActivityResult
} from '../../../types/activity.types';
import { colors } from '../../../utils/colors';

interface LoveLanguageQuestion {
  id: string;
  question: string;
  options: {
    text: string;
    language: 'words' | 'acts' | 'gifts' | 'time' | 'touch';
  }[];
}

interface LoveLanguageScore {
  words: number;
  acts: number;
  gifts: number;
  time: number;
  touch: number;
}

export function LoveLanguageQuizActivity({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  onError,
  theme = 'default',
  customData = {}
}: ActivityProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [scores, setScores] = useState<LoveLanguageScore>({
    words: 0,
    acts: 0,
    gifts: 0,
    time: 0,
    touch: 0
  });
  const [isCompleted, setIsCompleted] = useState(false);

  // Sample questions for love language quiz
  const questions: LoveLanguageQuestion[] = [
    {
      id: 'll-1',
      question: 'I feel most loved when my partner...',
      options: [
        { text: 'Tells me how much they appreciate me', language: 'words' },
        { text: 'Does something helpful for me', language: 'acts' },
        { text: 'Gives me a thoughtful gift', language: 'gifts' },
        { text: 'Spends quality time with me', language: 'time' },
        { text: 'Gives me a hug or holds my hand', language: 'touch' }
      ]
    },
    {
      id: 'll-2',
      question: 'When I want to show love, I prefer to...',
      options: [
        { text: 'Write a heartfelt note or text', language: 'words' },
        { text: 'Do something practical to help', language: 'acts' },
        { text: 'Buy or make something special', language: 'gifts' },
        { text: 'Plan a special date or activity', language: 'time' },
        { text: 'Give a massage or cuddle', language: 'touch' }
      ]
    },
    {
      id: 'll-3',
      question: 'I feel hurt when my partner...',
      options: [
        { text: 'Never says "I love you" or compliments me', language: 'words' },
        { text: 'Doesn\'t help with chores or tasks', language: 'acts' },
        { text: 'Forgets special occasions or gifts', language: 'gifts' },
        { text: 'Is always busy and never has time for me', language: 'time' },
        { text: 'Avoids physical affection', language: 'touch' }
      ]
    },
    {
      id: 'll-4',
      question: 'The best way to apologize to me is...',
      options: [
        { text: 'Saying sorry and explaining what went wrong', language: 'words' },
        { text: 'Doing something to make it right', language: 'acts' },
        { text: 'Bringing me a small gift or treat', language: 'gifts' },
        { text: 'Spending time talking it through', language: 'time' },
        { text: 'Giving me a hug and holding me', language: 'touch' }
      ]
    },
    {
      id: 'll-5',
      question: 'I feel most connected when...',
      options: [
        { text: 'We have deep conversations', language: 'words' },
        { text: 'We work together on projects', language: 'acts' },
        { text: 'We exchange meaningful gifts', language: 'gifts' },
        { text: 'We go on adventures together', language: 'time' },
        { text: 'We cuddle and are physically close', language: 'touch' }
      ]
    }
  ];

  const currentQuestion = questions[currentQuestionIndex];

  // Get theme colors
  const getThemeColors = useCallback(() => {
    switch (theme) {
      case 'romantic':
        return colors.solidColors.modulePink;
      case 'playful':
        return colors.solidColors.moduleOrange;
      case 'reflective':
        return colors.solidColors.moduleBlue;
      default:
        return colors.primary;
    }
  }, [theme]);

  const themeColors = getThemeColors();

  // Handle option selection
  const handleOptionSelect = useCallback((language: 'words' | 'acts' | 'gifts' | 'time' | 'touch') => {
    setScores(prev => ({
      ...prev,
      [language]: prev[language] + 1
    }));

    // Move to next question or complete quiz
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      setIsCompleted(true);
    }
  }, [currentQuestionIndex, questions.length]);

  // Get primary love language
  const getPrimaryLoveLanguage = useCallback(() => {
    const maxScore = Math.max(...Object.values(scores));
    const primaryLanguage = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0];
    return primaryLanguage as keyof LoveLanguageScore;
  }, [scores]);

  // Get love language details
  const getLoveLanguageDetails = useCallback((language: keyof LoveLanguageScore) => {
    const details = {
      words: {
        name: 'Words of Affirmation',
        description: 'You feel loved through verbal expressions of affection, appreciation, and encouragement.',
        icon: MessageCircle,
        color: colors.primary
      },
      acts: {
        name: 'Acts of Service',
        description: 'You feel loved when your partner does things to help you or make your life easier.',
        icon: Star,
        color: colors.success
      },
      gifts: {
        name: 'Receiving Gifts',
        description: 'You feel loved through thoughtful gifts that show your partner was thinking of you.',
        icon: Gift,
        color: colors.warning
      },
      time: {
        name: 'Quality Time',
        description: 'You feel loved when your partner gives you their undivided attention and time.',
        icon: Users,
        color: colors.info
      },
      touch: {
        name: 'Physical Touch',
        description: 'You feel loved through physical affection like hugs, kisses, and holding hands.',
        icon: Heart,
        color: colors.error
      }
    };
    return details[language];
  }, []);

  // Handle completion
  const handleComplete = useCallback(() => {
    const primaryLanguage = getPrimaryLoveLanguage();
    const languageDetails = getLoveLanguageDetails(primaryLanguage);

    const result: ActivityResult = {
      activityId,
      userId,
      coupleId,
      score: scores[primaryLanguage] * 10, // Score based on primary language strength
      points: 30, // Base points for completing the quiz
      completedAt: new Date().toISOString(),
      data: {
        scores,
        primaryLoveLanguage: primaryLanguage,
        loveLanguageDetails: languageDetails,
        totalQuestions: questions.length,
        activityType: 'love-language-quiz'
      }
    };

    onComplete(result);
  }, [activityId, userId, coupleId, scores, getPrimaryLoveLanguage, getLoveLanguageDetails, onComplete]);

  // Handle error
  const handleError = useCallback((error: string) => {
    const activityError: ActivityError = {
      activityId,
      userId,
      error,
      code: 'ACTIVITY_ERROR',
      timestamp: new Date().toISOString(),
      context: {
        currentQuestion: currentQuestion.id,
        scores,
        isCompleted
      }
    };

    onError(activityError);
  }, [activityId, userId, currentQuestion, scores, isCompleted, onError]);

  // Render quiz question
  const renderQuestion = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Heart size={24} color={colors.white} />
          <Text style={styles.headerTitle}>Love Language Quiz</Text>
        </View>
        <TouchableOpacity onPress={onExit} style={styles.exitButton}>
          <Text style={styles.exitButtonText}>Exit</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.questionContainer}>
          <Text style={styles.questionTitle}>{currentQuestion.question}</Text>
          <Text style={styles.progressText}>
            Question {currentQuestionIndex + 1} of {questions.length}
          </Text>
        </View>

        <View style={styles.optionsContainer}>
          {currentQuestion.options.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.optionButton, { backgroundColor: themeColors[0] }]}
              onPress={() => handleOptionSelect(option.language)}
            >
              <Text style={styles.optionText}>{option?.text || 'Option'}</Text>
              <ArrowRight size={20} color={colors.white} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  // Render results
  const renderResults = () => {
    const primaryLanguage = getPrimaryLoveLanguage();
    const languageDetails = getLoveLanguageDetails(primaryLanguage);
    const IconComponent = languageDetails.icon;

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Heart size={24} color={colors.white} />
            <Text style={styles.headerTitle}>Your Love Language</Text>
          </View>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.resultsContainer}>
            <View style={[styles.primaryLanguageCard, { backgroundColor: languageDetails.color }]}>
              <IconComponent size={48} color={colors.white} />
              <Text style={styles.primaryLanguageTitle}>{languageDetails.name}</Text>
              <Text style={styles.primaryLanguageDescription}>{languageDetails.description}</Text>
            </View>

            <View style={styles.scoresContainer}>
              <Text style={styles.scoresTitle}>Your Scores</Text>
              {Object.entries(scores).map(([language, score]) => {
                const details = getLoveLanguageDetails(language as keyof LoveLanguageScore);
                const Icon = details.icon;
                const isPrimary = language === primaryLanguage;

                return (
                  <View key={language} style={[styles.scoreItem, isPrimary && styles.primaryScoreItem]}>
                    <View style={styles.scoreItemLeft}>
                      <Icon size={20} color={isPrimary ? colors.white : details.color} />
                      <Text style={[styles.scoreItemText, isPrimary && styles.primaryScoreItemText]}>
                        {details.name}
                      </Text>
                    </View>
                    <Text style={[styles.scoreValue, isPrimary && styles.primaryScoreValue]}>
                      {score}
                    </Text>
                  </View>
                );
              })}
            </View>

            <TouchableOpacity
              style={[styles.completeButton, { backgroundColor: themeColors[0] }]}
              onPress={handleComplete}
            >
              <Text style={styles.completeButtonText}>Complete Activity</Text>
              <ArrowRight size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  };

  return isCompleted ? renderResults() : renderQuestion();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.primary,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  exitButton: {
    padding: 8,
  },
  exitButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  questionContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  questionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 32,
  },
  progressText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  optionText: {
    fontSize: 16,
    color: colors.white,
    flex: 1,
    marginRight: 12,
  },
  resultsContainer: {
    gap: 24,
  },
  primaryLanguageCard: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
  },
  primaryLanguageTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  primaryLanguageDescription: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 24,
    opacity: 0.9,
  },
  scoresContainer: {
    backgroundColor: colors.backgroundSecondary,
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  scoresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  scoreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  primaryScoreItem: {
    backgroundColor: colors.primary,
  },
  scoreItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  scoreItemText: {
    fontSize: 16,
    color: colors.textPrimary,
  },
  primaryScoreItemText: {
    color: colors.white,
  },
  scoreValue: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.textPrimary,
  },
  primaryScoreValue: {
    color: colors.white,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
