/**
 * Universal Activity Container Component
 * Wrapper component that can render any registered activity
 */

import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions
} from 'react-native';
import { X, Play, Pause, RotateCcw } from 'lucide-react-native';

import { colors } from '../../utils/colors';
import { logger } from '../../utils/logger';
import { activityRegistry } from '../../services/activity/activityRegistry';
import {
  ActivityContainerProps,
  ActivityState,
  ActivityStatus,
  ActivityResult,
  ActivityError,
  ActivityEvent,
  ActivityEventType
} from '../../types/activity.types';

const { width } = Dimensions.get('window');

export function ActivityContainer({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  onError,
  theme = 'default',
  customData = {},
  showHeader = true,
  showProgress = true,
  showExitButton = true
}: ActivityContainerProps) {
  const [activityState, setActivityState] = useState<ActivityState>({
    activityId,
    userId,
    coupleId,
    status: 'not_started',
    currentPhase: 'initializing',
    progress: 0,
    score: 0,
    points: 0,
    data: {}
  });

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get the activity component from registry
  const activityComponent = activityRegistry.getActivity(activityId);

  // Initialize activity state
  useEffect(() => {
    if (!activityComponent) {
      const errorMessage = `Activity not found: ${activityId}`;
      setError(errorMessage);
      setIsLoading(false);
      handleError({
        activityId,
        userId,
        error: errorMessage,
        code: 'ACTIVITY_NOT_FOUND',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Initialize activity state
    setActivityState(prev => ({
      ...prev,
      status: 'loading',
      currentPhase: 'initializing',
      startedAt: new Date().toISOString()
    }));

    // Emit activity started event
    emitActivityEvent('activity_started', {
      activityName: activityComponent.name,
      activityCategory: activityComponent.category,
      customData
    });

    setIsLoading(false);
  }, [activityId, activityComponent, userId, coupleId, customData]);

  // Handle activity completion
  const handleActivityComplete = useCallback((result: ActivityResult) => {
    setActivityState(prev => ({
      ...prev,
      status: 'completed',
      currentPhase: 'completed',
      progress: 100,
      score: result.score,
      points: result.points,
      completedAt: new Date().toISOString(),
      data: result.data
    }));

    // Emit activity completed event
    emitActivityEvent('activity_completed', {
      score: result.score,
      points: result.points,
      data: result.data
    });

    onComplete?.(result);
  }, [onComplete]);

  // Handle activity exit
  const handleActivityExit = useCallback(() => {
    setActivityState(prev => ({
      ...prev,
      status: 'exited',
      currentPhase: 'exited'
    }));

    // Emit activity exited event
    emitActivityEvent('activity_exited', {
      reason: 'user_exit',
      finalScore: activityState.score,
      finalPoints: activityState.points
    });

    onExit?.();
  }, [onExit, activityState.score, activityState.points]);

  // Handle activity error
  const handleError = useCallback((error: ActivityError) => {
    setActivityState(prev => ({
      ...prev,
      status: 'error',
      currentPhase: 'error',
      error: error.error
    }));

    // Emit activity error event
    emitActivityEvent('activity_error', {
      error: error.error,
      code: error.code,
      context: error.context
    });

    onError?.(error);
  }, [onError]);

  // Emit activity events
  const emitActivityEvent = useCallback((eventType: ActivityEventType, data: Record<string, any>) => {
    const event: ActivityEvent = {
      id: `${activityId}-${Date.now()}-${Math.random()}`,
      activityId,
      userId,
      coupleId,
      eventType,
      data,
      timestamp: new Date().toISOString(),
      metadata: {
        theme,
        customData
      }
    };

    // Log the event
    logger.info(`Activity Event: ${eventType}`, event);

    // TODO: Send event to analytics service
    // analyticsService.trackActivityEvent(event);
  }, [activityId, userId, coupleId, theme, customData]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    const newStatus: ActivityStatus = activityState.status === 'paused' ? 'in_progress' : 'paused';
    const eventType: ActivityEventType = newStatus === 'paused' ? 'activity_paused' : 'activity_resumed';

    setActivityState(prev => ({
      ...prev,
      status: newStatus,
      currentPhase: newStatus === 'paused' ? 'paused' : 'playing'
    }));

    emitActivityEvent(eventType, {
      previousStatus: activityState.status,
      newStatus
    });
  }, [activityState.status, emitActivityEvent]);

  // Handle restart
  const handleRestart = useCallback(() => {
    Alert.alert(
      'Restart Activity',
      'Are you sure you want to restart this activity? Your current progress will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Restart',
          style: 'destructive',
          onPress: () => {
            setActivityState(prev => ({
              ...prev,
              status: 'loading',
              currentPhase: 'restarting',
              progress: 0,
              score: 0,
              points: 0,
              data: {},
              error: undefined
            }));

            emitActivityEvent('activity_started', {
              activityName: activityComponent?.name,
              activityCategory: activityComponent?.category,
              customData,
              isRestart: true
            });
          }
        }
      ]
    );
  }, [activityComponent, customData, emitActivityEvent]);

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        {showHeader && (
          <View style={[styles.header, { backgroundColor: colors.primary }]}>
            <Text style={styles.headerTitle}>Loading Activity...</Text>
          </View>
        )}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Initializing activity...</Text>
        </View>
      </View>
    );
  }

  // Render error state
  if (error || !activityComponent) {
    return (
      <View style={styles.container}>
        {showHeader && (
          <View style={[styles.header, { backgroundColor: colors.error }]}>
            <Text style={styles.headerTitle}>Activity Error</Text>
            {showExitButton && (
              <TouchableOpacity onPress={handleActivityExit} style={styles.exitButton}>
                <X size={24} color={colors.white} />
              </TouchableOpacity>
            )}
          </View>
        )}
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Activity Not Available</Text>
          <Text style={styles.errorText}>
            {error || `The activity "${activityId}" could not be found or loaded.`}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleActivityExit}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Render activity component
  const ActivityComponent = activityComponent.component;

  return (
    <View style={styles.container}>
      {showHeader && (
        <View style={[styles.header, { backgroundColor: colors.primary }]}>
          <View style={styles.headerLeft}>
            <Text style={styles.headerTitle}>{activityComponent.name}</Text>
            {showProgress && (
              <Text style={styles.headerSubtitle}>
                {activityState.currentPhase} • {activityState.progress}%
              </Text>
            )}
          </View>
          
          <View style={styles.headerRight}>
            {activityState.status === 'in_progress' && (
              <TouchableOpacity onPress={handlePauseResume} style={styles.headerButton}>
                <Pause size={20} color={colors.white} />
              </TouchableOpacity>
            )}
            
            {activityState.status === 'paused' && (
              <TouchableOpacity onPress={handlePauseResume} style={styles.headerButton}>
                <Play size={20} color={colors.white} />
              </TouchableOpacity>
            )}
            
            <TouchableOpacity onPress={handleRestart} style={styles.headerButton}>
              <RotateCcw size={20} color={colors.white} />
            </TouchableOpacity>
            
            {showExitButton && (
              <TouchableOpacity onPress={handleActivityExit} style={styles.headerButton}>
                <X size={20} color={colors.white} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      <View style={styles.activityContainer}>
        <ActivityComponent
          activityId={activityId}
          userId={userId}
          coupleId={coupleId}
          onComplete={handleActivityComplete}
          onExit={handleActivityExit}
          onError={handleError}
          theme={theme}
          customData={customData}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.8,
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  exitButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  activityContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
