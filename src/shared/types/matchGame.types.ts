/**
 * Match Game Type Definitions
 * Clean separation of data structures for match game functionality
 */

// Base question interface (matches database schema)
export interface MatchGameQuestion {
  id: string;
  question_id: string;
  question_text: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  question_type: 'guess' | 'text' | 'list';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// User answer interface
export interface MatchGameUserAnswer {
  id: string;
  user_id: string;
  question_id: string;
  answer_text: string;
  created_at: string;
  updated_at: string;
}

// Game session interface
export interface MatchGameSession {
  id: string;
  couple_id: string;
  total_questions: number;
  correct_matches: number;
  completed: boolean;
  session_type: 'weekly' | 'custom' | 'practice';
  created_at: string;
  updated_at: string;
}

// Individual question result interface
export interface MatchGameResult {
  id: string;
  session_id: string;
  question_id: string;
  partner1_user_id: string;
  partner2_user_id: string;
  partner1_answer: string | null;
  partner2_answer: string | null;
  partner1_guess: string | null;
  partner2_guess: string | null;
  partner1_correct: boolean | null;
  partner2_correct: boolean | null;
  answered_at: string;
}

// Combined interfaces for app usage
export interface MatchGameQuestionWithAnswer extends MatchGameQuestion {
  user_answer?: string;
  partner_answer?: string;
  partner_guess?: string;
  is_correct?: boolean;
}

export interface MatchGameSessionWithResults extends MatchGameSession {
  results: MatchGameResult[];
  questions: MatchGameQuestion[];
}

// Game state interfaces
export interface MatchGamePlayer {
  user_id: string;
  name: string;
  score: number;
  correct_answers: number;
  total_answers: number;
}

export interface MatchGameStats {
  total_sessions: number;
  total_questions_answered: number;
  total_correct_matches: number;
  best_session_score: number;
  current_streak: number;
  longest_streak: number;
  favorite_category: string;
  average_session_duration: number;
}

export interface MatchGameGameState {
  session_id: string | null;
  current_question_index: number;
  questions: MatchGameQuestion[];
  player1: MatchGamePlayer;
  player2: MatchGamePlayer;
  phase: 'setup' | 'answering' | 'guessing' | 'revealing' | 'completed';
  is_loading: boolean;
  error: string | null;
}

// API request/response interfaces
export interface GetQuestionsRequest {
  count?: number;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  exclude_answered_by_user?: string;
  balanced?: boolean;
}

export interface SaveAnswerRequest {
  question_id: string;
  answer_text: string;
  user_id: string;
}

export interface CreateSessionRequest {
  couple_id: string;
  session_type?: 'weekly' | 'custom' | 'practice';
  question_count?: number;
}

export interface SaveResultRequest {
  session_id: string;
  question_id: string;
  partner1_user_id: string;
  partner2_user_id: string;
  partner1_answer: string;
  partner2_answer: string;
  partner1_guess: string;
  partner2_guess: string;
}

// Service interfaces
export interface MatchGameQuestionsService {
  getRandomQuestions(params: GetQuestionsRequest): Promise<MatchGameQuestion[]>;
  getBalancedQuestions(params: GetQuestionsRequest): Promise<MatchGameQuestion[]>;
  getQuestionsByCategory(category: string, count?: number): Promise<MatchGameQuestion[]>;
  getQuestionsByDifficulty(difficulty: 'easy' | 'medium' | 'hard', count?: number): Promise<MatchGameQuestion[]>;
  getAllCategories(): Promise<string[]>;
  getQuestionById(question_id: string): Promise<MatchGameQuestion | null>;
}

export interface MatchGameAnswersService {
  saveUserAnswer(answer: SaveAnswerRequest): Promise<MatchGameUserAnswer>;
  getUserAnswer(user_id: string, question_id: string): Promise<MatchGameUserAnswer | null>;
  getUserAnswers(user_id: string): Promise<MatchGameUserAnswer[]>;
  updateUserAnswer(answer_id: string, answer_text: string): Promise<MatchGameUserAnswer>;
  deleteUserAnswer(answer_id: string): Promise<void>;
  getUserAnsweredQuestions(user_id: string): Promise<string[]>;
}

export interface MatchGameSessionsService {
  createSession(session: CreateSessionRequest): Promise<MatchGameSession>;
  getSession(session_id: string): Promise<MatchGameSessionWithResults | null>;
  getUserSessions(user_id: string, limit?: number): Promise<MatchGameSession[]>;
  updateSession(session_id: string, updates: Partial<MatchGameSession>): Promise<MatchGameSession>;
  completeSession(session_id: string): Promise<MatchGameSession>;
  deleteSession(session_id: string): Promise<void>;
}

export interface MatchGameResultsService {
  saveResult(result: SaveResultRequest): Promise<MatchGameResult>;
  getSessionResults(session_id: string): Promise<MatchGameResult[]>;
  getUserStats(user_id: string): Promise<MatchGameStats>;
  getCoupleStats(couple_id: string): Promise<MatchGameStats>;
  getCategoryStats(user_id: string): Promise<Record<string, { total: number; correct: number; percentage: number }>>;
}

// Hook interfaces
export interface UseMatchGameQuestionsOptions {
  count?: number;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  balanced?: boolean;
  refetchOnMount?: boolean;
}

export interface UseMatchGameSessionOptions {
  session_id?: string;
  auto_create?: boolean;
  session_type?: 'weekly' | 'custom' | 'practice';
}

// Error class
export class MatchGameError extends Error {
  code: string;
  details?: any;

  constructor({ code, message, details }: { code: string; message: string; details?: any }) {
    super(message);
    this.name = 'MatchGameError';
    this.code = code;
    this.details = details;
  }
}

// Constants
export const MATCH_GAME_CATEGORIES = [
  'Food',
  'Entertainment', 
  'Personal',
  'Music',
  'Travel',
  'Relationship',
  'Work',
  'Hobbies',
  'Family',
  'Values'
] as const;

export const MATCH_GAME_DIFFICULTIES = ['easy', 'medium', 'hard'] as const;
export const MATCH_GAME_QUESTION_TYPES = ['guess', 'text', 'list'] as const;
export const MATCH_GAME_SESSION_TYPES = ['weekly', 'custom', 'practice'] as const;

export type MatchGameCategory = typeof MATCH_GAME_CATEGORIES[number];
export type MatchGameDifficulty = typeof MATCH_GAME_DIFFICULTIES[number];
export type MatchGameQuestionType = typeof MATCH_GAME_QUESTION_TYPES[number];
export type MatchGameSessionType = typeof MATCH_GAME_SESSION_TYPES[number];
