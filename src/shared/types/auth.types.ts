import { getCurrentUser, supabase } from '../services/supabase/client';
import { logger } from '../utils/logger';

// Type definitions
export interface AuthUser {
  id: string;
  email?: string;
  email_confirmed_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  user: AuthUser;
}

export interface AuthEvent {
  event: 'SIGNED_IN' | 'SIGNED_OUT' | 'TOKEN_REFRESHED' | 'USER_UPDATED' | 'PASSWORD_RECOVERY' | 'INITIAL_SESSION' | 'MFA_CHALLENGE_VERIFIED';
  session: AuthSession | null;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface SignUpData {
  email: string;
  password: string;
  partner1Name: string;
  partner1Icon: string;
  partner2Name: string;
  partner2Icon: string;
}

export interface UserProfile {
  partner1: { name: string; icon: string };
  partner2: { name: string; icon: string };
  partner1PhotoUrl?: string | null;
  partner2PhotoUrl?: string | null;
  relationshipStartDate?: string | null;
  isComplete: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface AuthData {
  user: AuthUser;
  profile: UserProfile | null;
}

export type OnAuthStateChangeCallback = (event: AuthEvent['event'], session: AuthSession | null) => void;

async function signUp(data: SignUpData): Promise<AuthData> {
  logger.info('Attempting to sign up user:', { email: data.email });
  const { data: authData, error: authError } = await supabase.auth.signUp({ email: data.email, password: data.password });
  if (authError) throw new Error(authError.message);
  if (!authData.user) throw new Error('Failed to create user account');

  // Create profile record
  const { error: profileError } = await supabase.from('profiles').insert({
    id: authData.user.id,
    partner1_name: data.partner1Name,
    partner1_icon: data.partner1Icon,
    partner2_name: data.partner2Name,
    partner2_icon: data.partner2Icon,
    is_complete: true,
  });
  if (profileError) throw new Error(`Failed to create user profile: ${profileError.message}`);

  const profile: UserProfile = {
    partner1: { name: data.partner1Name, icon: data.partner1Icon },
    partner2: { name: data.partner2Name, icon: data.partner2Icon },
    isComplete: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };
  return { user: authData.user, profile };
}

async function signIn(data: SignInData): Promise<AuthData> {
  logger.info('Attempting to sign in user:', { email: data.email });
  const { data: authData, error } = await supabase.auth.signInWithPassword({ email: data.email, password: data.password });
  if (error) throw new Error(error.message);
  if (!authData.user) throw new Error('Failed to sign in');
  const profile = await getUserProfile(authData.user.id);
  return { user: authData.user, profile };
}

async function signOut(): Promise<void> {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
}

async function getCurrent(): Promise<AuthData | null> {
  const user = await getCurrentUser();
  if (!user) return null;
  const profile = await getUserProfile(user.id);
  return { user, profile };
}

// Alias that returns the same AuthData shape for convenience
async function getCurrentUserData(): Promise<AuthData | null> {
  return getCurrent();
}

async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).single();
  if (error || !data) return null;
  return {
    partner1: { name: data.partner1_name, icon: data.partner1_icon },
    partner2: { name: data.partner2_name, icon: data.partner2_icon },
    partner1PhotoUrl: data.partner1_profile_picture || null,
    partner2PhotoUrl: data.partner2_profile_picture || null,
    relationshipStartDate: (data as any).relationship_start_date || null,
    isComplete: data.is_complete,
    createdAt: data.created_at ? new Date(data.created_at).getTime() : 0,
    updatedAt: data.updated_at ? new Date(data.updated_at).getTime() : 0,
  };
}

async function updateUserProfile(userId: string, profile: Partial<UserProfile>): Promise<void> {
  const updateData: Record<string, any> = {};
  if (profile.partner1) {
    if (profile.partner1.name !== undefined) updateData.partner1_name = profile.partner1.name;
    if (profile.partner1.icon !== undefined) updateData.partner1_icon = profile.partner1.icon;
  }
  if (profile.partner2) {
    if (profile.partner2.name !== undefined) updateData.partner2_name = profile.partner2.name;
    if (profile.partner2.icon !== undefined) updateData.partner2_icon = profile.partner2.icon;
  }
  if (profile.partner1PhotoUrl !== undefined) {
    updateData.partner1_profile_picture = profile.partner1PhotoUrl;
  }
  if (profile.partner2PhotoUrl !== undefined) {
    updateData.partner2_profile_picture = profile.partner2PhotoUrl;
  }
  if ((profile as any).relationshipStartDate !== undefined) {
    updateData.relationship_start_date = (profile as any).relationshipStartDate;
  }
  if (profile.isComplete !== undefined) {
    updateData.is_complete = profile.isComplete;
  }
  const { error } = await supabase.from('profiles').update(updateData).eq('id', userId);
  if (error) throw new Error('Failed to update profile');
}

function onAuthStateChange(callback: OnAuthStateChangeCallback) {
  return supabase.auth.onAuthStateChange(callback);
}

async function resetPassword(email: string): Promise<void> {
  const { error } = await supabase.auth.resetPasswordForEmail(email);
  if (error) throw new Error(error.message);
}

export const auth = {
  signIn,
  signUp,
  signOut,
  getCurrent,
  getCurrentUserData,
  getUserProfile,
  updateUserProfile,
  onAuthStateChange,
  resetPassword,
};

// Types are already exported above
