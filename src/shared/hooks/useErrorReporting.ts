import { useCallback, useState } from 'react';
import { Alert } from 'react-native';
import { secureStorage } from '../../utils/secureStorage';
import { ErrorContext } from '../types';
import { errorHandler } from '../utils/errorHandler';
import { logger } from '../utils/logger';

interface ErrorReport {
  id: string;
  error: {
    message: string;
    stack?: string;
    name: string;
  };
  userMessage: string;
  context: ErrorContext;
  deviceInfo: {
    platform: string;
    timestamp: string;
    userAgent: string;
  };
  appVersion: string;
  timestamp: string;
}

export const useErrorReporting = () => {
  const [isReporting, setIsReporting] = useState(false);

  const generateErrorReport = useCallback(async (
    error: Error,
    userMessage: string,
    context: ErrorContext = {}
  ): Promise<ErrorReport> => {
    try {
      const userProfile = await secureStorage.getItem('user_profile');

      return {
        id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        userMessage,
        context: {
          ...context,
          timestamp: new Date().toISOString(),
        },
        deviceInfo: {
          platform: 'React Native',
          timestamp: new Date().toISOString(),
          userAgent: 'Everlasting Us App',
        },
        appVersion: '1.0.0',
        timestamp: new Date().toISOString(),
      };
    } catch (err) {
      logger.error('Error generating report:', err);
      throw new Error('Failed to generate error report');
    }
  }, []);

  const saveErrorReport = useCallback(async (report: ErrorReport): Promise<void> => {
    try {
      const reports = await secureStorage.getItem<ErrorReport[]>('error_reports') || [];
      reports.push(report);

      // Keep only last 20 reports to prevent storage bloat
      if (reports.length > 20) {
        reports.splice(0, reports.length - 20);
      }

      await secureStorage.setItem('error_reports', reports);

      // Log the report for monitoring
      logger.error('Error report saved:', {
        reportId: report.id,
        errorMessage: report.error.message,
        userMessage: report.userMessage,
        context: report.context,
      });
    } catch (err) {
      logger.error('Error saving report:', err);
      throw new Error('Failed to save error report');
    }
  }, []);

  const submitErrorReport = useCallback(async (
    error: Error,
    userMessage: string,
    context: ErrorContext = {}
  ): Promise<boolean> => {
    if (!userMessage.trim()) {
      Alert.alert('Oops!', 'Please tell us what happened so we can help! 💕');
      return false;
    }

    setIsReporting(true);

    try {
      const report = await generateErrorReport(error, userMessage, context);
      await saveErrorReport(report);

      Alert.alert(
        'Thank you! 💕',
        'Your report has been saved and will help us make the app better! We appreciate you taking the time to help us improve.',
        [{ text: 'You\'re welcome!', style: 'default' }]
      );

      return true;
    } catch (err) {
      logger.error('Error submitting report:', err);
      Alert.alert(
        'Oops!',
        'There was a problem saving your report, but we still appreciate you trying to help! 💕'
      );
      return false;
    } finally {
      setIsReporting(false);
    }
  }, [generateErrorReport, saveErrorReport]);

  const getStoredReports = useCallback(async (): Promise<ErrorReport[]> => {
    try {
      return await secureStorage.getItem<ErrorReport[]>('error_reports') || [];
    } catch (err) {
      logger.error('Error retrieving reports:', err);
      return [];
    }
  }, []);

  const clearStoredReports = useCallback(async (): Promise<void> => {
    try {
      await secureStorage.removeItem('error_reports');
      logger.info('Error reports cleared');
    } catch (err) {
      logger.error('Error clearing reports:', err);
    }
  }, []);

  const reportError = useCallback((error: Error, context?: ErrorContext) => {
    errorHandler.handleError(error, context);
  }, []);

  const showErrorReportDialog = useCallback((
    error: Error,
    context: ErrorContext = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      Alert.alert(
        'Something went wrong 💔',
        'Would you like to help us fix this by sending a quick report?',
        [
          {
            text: 'Not now',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: 'Help out! 💕',
            style: 'default',
            onPress: () => {
              // This would typically open a modal or navigate to a report screen
              // For now, we'll just log the error
              reportError(error, context);
              resolve(true);
            },
          },
        ]
      );
    });
  }, [reportError]);

  return {
    isReporting,
    submitErrorReport,
    getStoredReports,
    clearStoredReports,
    reportError,
    showErrorReportDialog,
    generateErrorReport,
  };
};
