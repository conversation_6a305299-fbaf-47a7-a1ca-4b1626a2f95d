/**
 * Unified Favorites Hook
 * Provides consistent favorite functionality across all features
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../journeys/onboarding/useAuth';
import { 
  favoritesService, 
  FavoriteItemType, 
  FavoriteItem, 
  FavoriteMetadata 
} from '../services/favoritesService';
import { logger } from '../utils/logger';

export interface UseFavoritesReturn {
  favorites: FavoriteItem[];
  favoriteStatuses: Record<string, boolean>;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  addFavorite: (itemId: string, itemType: FavoriteItemType, sourceTable?: string, metadata?: FavoriteMetadata) => Promise<boolean>;
  removeFavorite: (itemId: string, itemType: FavoriteItemType) => Promise<boolean>;
  toggleFavorite: (itemId: string, itemType: FavoriteItemType, sourceTable?: string, metadata?: FavoriteMetadata) => Promise<boolean>;
  isFavorited: (itemId: string, itemType: FavoriteItemType) => boolean;
  getFavoritesByType: (itemType: FavoriteItemType) => FavoriteItem[];
  getFavoriteCount: (itemType: FavoriteItemType) => number;
  refreshFavorites: () => Promise<void>;
}

export function useFavorites(): UseFavoritesReturn {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [favoriteStatuses, setFavoriteStatuses] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load all favorites for the user
  const loadFavorites = useCallback(async () => {
    if (!user?.id) {
      setFavorites([]);
      setFavoriteStatuses({});
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const userFavorites = await favoritesService.getAllFavorites(user.id);
      setFavorites(userFavorites);

      // Create status lookup for quick access
      const statuses: Record<string, boolean> = {};
      userFavorites.forEach(fav => {
        const key = `${fav.item_id}:${fav.item_type}`;
        statuses[key] = true;
      });
      setFavoriteStatuses(statuses);

      logger.info('Favorites loaded:', { count: userFavorites.length });
    } catch (error) {
      logger.error('Error loading favorites:', error);
      setError('Failed to load favorites');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Load favorites when user changes
  useEffect(() => {
    loadFavorites();
  }, [loadFavorites]);

  // Add favorite
  const addFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot add favorite: no user logged in');
      return false;
    }

    try {
      const success = await favoritesService.addFavorite(user.id, itemId, itemType, sourceTable, metadata);
      
      if (success) {
        // Update local state optimistically
        const key = `${itemId}:${itemType}`;
        setFavoriteStatuses(prev => ({ ...prev, [key]: true }));
        
        // Refresh favorites to get the complete data
        await loadFavorites();
      }
      
      return success;
    } catch (error) {
      logger.error('Error adding favorite:', error);
      return false;
    }
  }, [user?.id, loadFavorites]);

  // Remove favorite
  const removeFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot remove favorite: no user logged in');
      return false;
    }

    try {
      const success = await favoritesService.removeFavorite(user.id, itemId, itemType);
      
      if (success) {
        // Update local state optimistically
        const key = `${itemId}:${itemType}`;
        setFavoriteStatuses(prev => ({ ...prev, [key]: false }));
        
        // Refresh favorites to get the complete data
        await loadFavorites();
      }
      
      return success;
    } catch (error) {
      logger.error('Error removing favorite:', error);
      return false;
    }
  }, [user?.id, loadFavorites]);

  // Toggle favorite
  const toggleFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot toggle favorite: no user logged in');
      return false;
    }

    try {
      const success = await favoritesService.toggleFavorite(user.id, itemId, itemType, sourceTable, metadata);
      
      if (success) {
        // Refresh favorites to get the updated state
        await loadFavorites();
      }
      
      return success;
    } catch (error) {
      logger.error('Error toggling favorite:', error);
      return false;
    }
  }, [user?.id, loadFavorites]);

  // Check if item is favorited
  const isFavorited = useCallback((itemId: string, itemType: FavoriteItemType): boolean => {
    const key = `${itemId}:${itemType}`;
    return favoriteStatuses[key] || false;
  }, [favoriteStatuses]);

  // Get favorites by type
  const getFavoritesByType = useCallback((itemType: FavoriteItemType): FavoriteItem[] => {
    return favorites.filter(fav => fav.item_type === itemType);
  }, [favorites]);

  // Get favorite count by type
  const getFavoriteCount = useCallback((itemType: FavoriteItemType): number => {
    return favorites.filter(fav => fav.item_type === itemType).length;
  }, [favorites]);

  // Refresh favorites
  const refreshFavorites = useCallback(async () => {
    await loadFavorites();
  }, [loadFavorites]);

  return {
    favorites,
    favoriteStatuses,
    isLoading,
    error,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorited,
    getFavoritesByType,
    getFavoriteCount,
    refreshFavorites
  };
}
