/**
 * Enterprise Logger
 *
 * Centralized logging system for the application
 */

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  context?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  source?: string;
}

class EnterpriseLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  private createLogEntry(level: LogLevel, message: string, context?: Record<string, any>): LogEntry {
    return {
      level,
      message,
      timestamp: Date.now(),
      context,
      source: 'app'
    };
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);

    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output for development
    if (__DEV__) {
      const timestamp = new Date(entry.timestamp).toISOString();
      const contextStr = entry.context ? JSON.stringify(entry.context) : '';
      console.log(`[${timestamp}] ${entry.level.toUpperCase()}: ${entry.message} ${contextStr}`);
    }
  }

  debug(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
    this.addLog(entry);
  }

  info(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    this.addLog(entry);
  }

  warn(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context);
    this.addLog(entry);
  }

  error(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context);
    this.addLog(entry);
  }

  fatal(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, context);
    this.addLog(entry);
  }

  logUserAction(action: string, userId?: string, context?: Record<string, any>): void {
    this.info(`User Action: ${action}`, { ...context, userId });
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (!level) {
      return [...this.logs];
    }
    return this.logs.filter(log => log.level === level);
  }

  clearLogs(): void {
    this.logs = [];
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
  getStats() {
    const totalLogs = this.logs.length;
    const logsByLevel = {
      DEBUG: this.logs.filter(log => log.level === LogLevel.DEBUG).length,
      INFO: this.logs.filter(log => log.level === LogLevel.INFO).length,
      WARN: this.logs.filter(log => log.level === LogLevel.WARN).length,
      ERROR: this.logs.filter(log => log.level === LogLevel.ERROR).length,
      FATAL: this.logs.filter(log => log.level === LogLevel.FATAL).length
    };

    return {
      totalLogs,
      logsByLevel,
      debugCount: logsByLevel.DEBUG,
      infoCount: logsByLevel.INFO,
      warnCount: logsByLevel.WARN,
      errorCount: logsByLevel.ERROR + logsByLevel.FATAL,
      lastLog: this.logs[this.logs.length - 1]
    };
  }
}

export const enterpriseLogger = new EnterpriseLogger();

// Legacy exports for backward compatibility
export const info = (message: string, context?: Record<string, any>) => enterpriseLogger.info(message, context);
export const error = (message: string, context?: Record<string, any>) => enterpriseLogger.error(message, context);
export const logUserAction = (action: string, userId?: string, context?: Record<string, any>) =>
  enterpriseLogger.logUserAction(action, userId, context);
