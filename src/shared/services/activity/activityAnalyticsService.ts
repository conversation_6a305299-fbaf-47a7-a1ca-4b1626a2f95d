/**
 * Activity Analytics Service
 * Tracks activity usage, completion rates, and user engagement
 */

import { supabase } from '../supabase/client';

export interface ActivityAnalytics {
  activityId: string;
  userId: string;
  coupleId: string;
  eventType: 'start' | 'complete' | 'exit' | 'error' | 'pause' | 'resume';
  timestamp: string;
  duration?: number; // in seconds
  score?: number;
  points?: number;
  metadata?: Record<string, any>;
}

export interface ActivityStats {
  totalStarts: number;
  totalCompletions: number;
  completionRate: number;
  averageDuration: number;
  averageScore: number;
  totalPoints: number;
  lastPlayed?: string;
}

export interface UserActivityStats {
  userId: string;
  totalActivities: number;
  totalPoints: number;
  completionRate: number;
  favoriteCategory: string;
  streak: number;
  lastActivityDate?: string;
}

interface ActivitySessionRecord {
  id: string;
  activity_id: string;
  user_id: string;
  couple_id: string;
  status: string;
  started_at: string;
  completed_at: string | null;
  score: number;
  points: number;
  data: Record<string, any>;
  events: any[];
  created_at: string;
  updated_at: string;
}

class ActivityAnalyticsService {
  private static instance: ActivityAnalyticsService;
  private eventQueue: ActivityAnalytics[] = [];
  private isProcessing = false;

  static getInstance(): ActivityAnalyticsService {
    if (!ActivityAnalyticsService.instance) {
      ActivityAnalyticsService.instance = new ActivityAnalyticsService();
    }
    return ActivityAnalyticsService.instance;
  }

  /**
   * Track an activity event
   */
  async trackEvent(analytics: ActivityAnalytics): Promise<void> {
    try {
      // Add to queue for batch processing
      this.eventQueue.push(analytics);

      // Process queue if not already processing
      if (!this.isProcessing) {
        await this.processQueue();
      }
    } catch (error) {
      console.error('Error tracking activity event:', error);
    }
  }

  /**
   * Process the event queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const events = [...this.eventQueue];
      this.eventQueue = [];

      // Batch insert events
      const { error } = await supabase
        .from('activity_analytics')
        .insert(events.map(event => ({
          activity_id: event.activityId,
          user_id: event.userId,
          couple_id: event.coupleId,
          event_type: event.eventType,
          timestamp: event.timestamp,
          duration: event.duration,
          score: event.score,
          points: event.points,
          metadata: event.metadata
        })));

      if (error) {
        console.error('Error inserting analytics events:', error);
        // Re-queue events for retry
        this.eventQueue.unshift(...events);
      }
    } catch (error) {
      console.error('Error processing analytics queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get activity statistics
   */
  async getActivityStats(activityId: string): Promise<ActivityStats> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('activity_id', activityId);

      if (error) {
        console.error('Error fetching activity stats:', error);
        return this.getDefaultStats();
      }

      const sessions: ActivitySessionRecord[] = data || [];
      const starts = sessions.filter((s: ActivitySessionRecord) => s.status !== 'not_started');
      const completions = sessions.filter((s: ActivitySessionRecord) => s.status === 'completed');

      return {
        totalStarts: starts.length,
        totalCompletions: completions.length,
        completionRate: starts.length > 0 ? (completions.length / starts.length) * 100 : 0,
        averageDuration: this.calculateAverageDuration(completions),
        averageScore: this.calculateAverageScore(completions),
        totalPoints: completions.reduce((sum: number, c: ActivitySessionRecord) => sum + (c.points || 0), 0),
        lastPlayed: this.getLastPlayedDate(sessions)
      };
    } catch (error) {
      console.error('Error getting activity stats:', error);
      return this.getDefaultStats();
    }
  }

  /**
   * Get user activity statistics
   */
  async getUserActivityStats(userId: string): Promise<UserActivityStats> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user activity stats:', error);
        return this.getDefaultUserStats(userId);
      }

      const sessions: ActivitySessionRecord[] = data || [];
      const completions = sessions.filter((s: ActivitySessionRecord) => s.status === 'completed');
      const uniqueActivities = new Set(completions.map((c: ActivitySessionRecord) => c.activity_id));

      return {
        userId,
        totalActivities: uniqueActivities.size,
        totalPoints: completions.reduce((sum: number, c: ActivitySessionRecord) => sum + (c.points || 0), 0),
        completionRate: this.calculateUserCompletionRate(sessions),
        favoriteCategory: await this.getFavoriteCategory(userId),
        streak: await this.calculateStreak(userId),
        lastActivityDate: this.getLastPlayedDate(sessions)
      };
    } catch (error) {
      console.error('Error getting user activity stats:', error);
      return this.getDefaultUserStats(userId);
    }
  }

  /**
   * Get couple activity statistics
   */
  async getCoupleActivityStats(coupleId: string): Promise<{
    totalActivities: number;
    totalPoints: number;
    completionRate: number;
    sharedActivities: string[];
  }> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('couple_id', coupleId);

      if (error) {
        console.error('Error fetching couple activity stats:', error);
        return {
          totalActivities: 0,
          totalPoints: 0,
          completionRate: 0,
          sharedActivities: []
        };
      }

      const sessions: ActivitySessionRecord[] = data || [];
      const completions = sessions.filter((s: ActivitySessionRecord) => s.status === 'completed');
      const uniqueActivities = new Set(completions.map((c: ActivitySessionRecord) => c.activity_id));

      return {
        totalActivities: uniqueActivities.size,
        totalPoints: completions.reduce((sum: number, c: ActivitySessionRecord) => sum + (c.points || 0), 0),
        completionRate: this.calculateUserCompletionRate(sessions),
        sharedActivities: Array.from(uniqueActivities)
      };
    } catch (error) {
      console.error('Error getting couple activity stats:', error);
      return {
        totalActivities: 0,
        totalPoints: 0,
        completionRate: 0,
        sharedActivities: []
      };
    }
  }

  /**
   * Track activity start
   */
  async trackActivityStart(activityId: string, userId: string, coupleId: string): Promise<void> {
    await this.trackEvent({
      activityId,
      userId,
      coupleId,
      eventType: 'start',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Track activity completion
   */
  async trackActivityComplete(
    activityId: string,
    userId: string,
    coupleId: string,
    duration: number,
    score?: number,
    points?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent({
      activityId,
      userId,
      coupleId,
      eventType: 'complete',
      timestamp: new Date().toISOString(),
      duration,
      score,
      points,
      metadata
    });
  }

  /**
   * Track activity exit
   */
  async trackActivityExit(activityId: string, userId: string, coupleId: string): Promise<void> {
    await this.trackEvent({
      activityId,
      userId,
      coupleId,
      eventType: 'exit',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Track activity error
   */
  async trackActivityError(
    activityId: string,
    userId: string,
    coupleId: string,
    error: any
  ): Promise<void> {
    await this.trackEvent({
      activityId,
      userId,
      coupleId,
      eventType: 'error',
      timestamp: new Date().toISOString(),
      metadata: { error: error.message || 'Unknown error' }
    });
  }

  // Helper methods
  private calculateAverageDuration(completions: ActivitySessionRecord[]): number {
    if (completions.length === 0) return 0;
    const total = completions.reduce((sum, c) => {
      if (c.completed_at && c.started_at) {
        const duration = new Date(c.completed_at).getTime() - new Date(c.started_at).getTime();
        return sum + duration;
      }
      return sum;
    }, 0);
    return total / completions.length;
  }

  private calculateAverageScore(completions: ActivitySessionRecord[]): number {
    if (completions.length === 0) return 0;
    const total = completions.reduce((sum, c) => sum + (c.score || 0), 0);
    return total / completions.length;
  }

  private getLastPlayedDate(sessions: ActivitySessionRecord[]): string | undefined {
    if (sessions.length === 0) return undefined;
    const sorted = sessions.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    return sorted[0].updated_at;
  }

  private calculateUserCompletionRate(sessions: ActivitySessionRecord[]): number {
    const starts = sessions.filter(s => s.status !== 'not_started');
    const completions = sessions.filter(s => s.status === 'completed');
    return starts.length > 0 ? (completions.length / starts.length) * 100 : 0;
  }

  private async getFavoriteCategory(userId: string): Promise<string> {
    // This would need to be implemented based on your activity categorization
    return 'getting-to-know';
  }

  private async calculateStreak(userId: string): Promise<number> {
    // This would need to be implemented based on your streak calculation logic
    return 0;
  }

  private getDefaultStats(): ActivityStats {
    return {
      totalStarts: 0,
      totalCompletions: 0,
      completionRate: 0,
      averageDuration: 0,
      averageScore: 0,
      totalPoints: 0
    };
  }

  private getDefaultUserStats(userId: string): UserActivityStats {
    return {
      userId,
      totalActivities: 0,
      totalPoints: 0,
      completionRate: 0,
      favoriteCategory: 'getting-to-know',
      streak: 0
    };
  }
}

export const activityAnalyticsService = ActivityAnalyticsService.getInstance();
