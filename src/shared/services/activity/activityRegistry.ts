/**
 * Activity Registry Service
 * Central registry for managing modular activity components
 */

import { logger } from '../../utils/logger';
import {
  ActivityComponent,
  ActivityRegistry,
  ActivityCategory,
  ActivityFilterOptions,
  ActivitySortOptions,
  ActivityQueryOptions
} from '../../types/activity.types';

class ActivityRegistryService implements ActivityRegistry {
  private activities: Map<string, ActivityComponent> = new Map();
  private categories: Map<ActivityCategory, ActivityComponent[]> = new Map();

  constructor() {
    // Initialize category maps
    const categories: ActivityCategory[] = [
      'getting-to-know',
      'communication', 
      'fun',
      'reflection',
      'skills',
      'planning'
    ];

    categories.forEach(category => {
      this.categories.set(category, []);
    });

    logger.info('Activity Registry Service initialized');
  }

  /**
   * Register a new activity component
   */
  registerActivity(activity: ActivityComponent): void {
    try {
      // Validate activity component
      this.validateActivityComponent(activity);

      // Check if activity already exists
      if (this.activities.has(activity.id)) {
        logger.warn(`Activity with ID ${activity.id} already exists. Updating existing activity.`);
      }

      // Register the activity
      this.activities.set(activity.id, activity);

      // Add to category
      const categoryActivities = this.categories.get(activity.category) || [];
      const existingIndex = categoryActivities.findIndex(a => a.id === activity.id);
      
      if (existingIndex >= 0) {
        categoryActivities[existingIndex] = activity;
      } else {
        categoryActivities.push(activity);
      }
      
      this.categories.set(activity.category, categoryActivities);

      logger.info(`Activity registered: ${activity.name} (${activity.id})`);
    } catch (error) {
      logger.error(`Failed to register activity ${activity.id}:`, error);
      throw error;
    }
  }

  /**
   * Unregister an activity component
   */
  unregisterActivity(activityId: string): void {
    try {
      const activity = this.activities.get(activityId);
      if (!activity) {
        logger.warn(`Activity with ID ${activityId} not found for unregistration`);
        return;
      }

      // Remove from activities map
      this.activities.delete(activityId);

      // Remove from category
      const categoryActivities = this.categories.get(activity.category) || [];
      const filteredActivities = categoryActivities.filter(a => a.id !== activityId);
      this.categories.set(activity.category, filteredActivities);

      logger.info(`Activity unregistered: ${activity.name} (${activityId})`);
    } catch (error) {
      logger.error(`Failed to unregister activity ${activityId}:`, error);
      throw error;
    }
  }

  /**
   * Get a specific activity by ID
   */
  getActivity(activityId: string): ActivityComponent | undefined {
    return this.activities.get(activityId);
  }

  /**
   * Get all activities in a specific category
   */
  getActivitiesByCategory(category: ActivityCategory): ActivityComponent[] {
    return this.categories.get(category) || [];
  }

  /**
   * Get all registered activities
   */
  getAllActivities(): ActivityComponent[] {
    return Array.from(this.activities.values());
  }

  /**
   * Get only available activities
   */
  getAvailableActivities(): ActivityComponent[] {
    return this.getAllActivities().filter(activity => activity.metadata.isAvailable);
  }

  /**
   * Query activities with filters and sorting
   */
  queryActivities(options: ActivityQueryOptions = {}): ActivityComponent[] {
    let activities = this.getAllActivities();

    // Apply filters
    if (options.filters) {
      activities = this.applyFilters(activities, options.filters);
    }

    // Apply sorting
    if (options.sort) {
      activities = this.applySorting(activities, options.sort);
    }

    // Apply pagination
    if (options.limit || options.offset) {
      const offset = options.offset || 0;
      const limit = options.limit || activities.length;
      activities = activities.slice(offset, offset + limit);
    }

    return activities;
  }

  /**
   * Get activity statistics
   */
  getActivityStats(): {
    totalActivities: number;
    availableActivities: number;
    activitiesByCategory: Record<ActivityCategory, number>;
    activitiesByDifficulty: Record<string, number>;
  } {
    const allActivities = this.getAllActivities();
    const availableActivities = this.getAvailableActivities();

    const activitiesByCategory: Record<ActivityCategory, number> = {
      'getting-to-know': 0,
      'communication': 0,
      'fun': 0,
      'reflection': 0,
      'skills': 0,
      'planning': 0
    };

    const activitiesByDifficulty: Record<string, number> = {
      easy: 0,
      medium: 0,
      hard: 0
    };

    allActivities.forEach(activity => {
      activitiesByCategory[activity.category]++;
      activitiesByDifficulty[activity.metadata.difficulty]++;
    });

    return {
      totalActivities: allActivities.length,
      availableActivities: availableActivities.length,
      activitiesByCategory,
      activitiesByDifficulty
    };
  }

  /**
   * Validate activity component structure
   */
  private validateActivityComponent(activity: ActivityComponent): void {
    if (!activity.id || typeof activity.id !== 'string') {
      throw new Error('Activity must have a valid string ID');
    }

    if (!activity.name || typeof activity.name !== 'string') {
      throw new Error('Activity must have a valid string name');
    }

    if (!activity.component || typeof activity.component !== 'function') {
      throw new Error('Activity must have a valid React component');
    }

    if (!activity.metadata) {
      throw new Error('Activity must have metadata');
    }

    if (!activity.metadata.id || activity.metadata.id !== activity.id) {
      throw new Error('Activity metadata ID must match activity ID');
    }

    if (!activity.supabaseTable || typeof activity.supabaseTable !== 'string') {
      throw new Error('Activity must have a valid Supabase table name');
    }

    // Validate category
    const validCategories: ActivityCategory[] = [
      'getting-to-know',
      'communication',
      'fun', 
      'reflection',
      'skills',
      'planning'
    ];

    if (!validCategories.includes(activity.category)) {
      throw new Error(`Invalid activity category: ${activity.category}`);
    }
  }

  /**
   * Apply filters to activities list
   */
  private applyFilters(activities: ActivityComponent[], filters: ActivityFilterOptions): ActivityComponent[] {
    return activities.filter(activity => {
      // Category filter
      if (filters.category && activity.category !== filters.category) {
        return false;
      }

      // Difficulty filter
      if (filters.difficulty && activity.metadata.difficulty !== filters.difficulty) {
        return false;
      }

      // Theme filter
      if (filters.theme && activity.metadata.theme !== filters.theme) {
        return false;
      }

      // Availability filter
      if (filters.isAvailable !== undefined && activity.metadata.isAvailable !== filters.isAvailable) {
        return false;
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const activityTags = activity.metadata.tags || [];
        const hasMatchingTag = filters.tags.some(tag => 
          activityTags.some(activityTag => 
            activityTag.toLowerCase().includes(tag.toLowerCase())
          )
        );
        if (!hasMatchingTag) {
          return false;
        }
      }

      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesName = activity.name.toLowerCase().includes(searchTerm);
        const matchesDescription = activity.metadata.description.toLowerCase().includes(searchTerm);
        const matchesTags = (activity.metadata.tags || []).some(tag => 
          tag.toLowerCase().includes(searchTerm)
        );
        
        if (!matchesName && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Apply sorting to activities list
   */
  private applySorting(activities: ActivityComponent[], sort: ActivitySortOptions): ActivityComponent[] {
    return activities.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sort.field) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'category':
          aValue = a.category;
          bValue = b.category;
          break;
        case 'difficulty':
          const difficultyOrder = { easy: 1, medium: 2, hard: 3 };
          aValue = difficultyOrder[a.metadata.difficulty];
          bValue = difficultyOrder[b.metadata.difficulty];
          break;
        case 'points':
          aValue = a.metadata.points;
          bValue = b.metadata.points;
          break;
        case 'createdAt':
          aValue = new Date(a.metadata.createdAt).getTime();
          bValue = new Date(b.metadata.createdAt).getTime();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }
}

// Create singleton instance
export const activityRegistry = new ActivityRegistryService();

// Export the class for testing
export { ActivityRegistryService };
