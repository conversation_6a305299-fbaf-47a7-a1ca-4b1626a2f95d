import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { darkTheme, getAdaptiveColors, lightTheme, ThemeMode } from '../../utils/colors';
import { logger } from '../../utils/logger';
import { secureStorage } from '../../utils/secureStorage';
import { simpleErrorService } from '../system/simpleErrorService';

export interface AppSettings {
  darkMode: boolean;
  language: string;
  autoSave: boolean;
  dataSync: boolean;
  accessibility: boolean;
  notifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  fontSize: 'small' | 'medium' | 'large';
  theme: ThemeMode;
}

interface SettingsContextType {
  settings: AppSettings;
  isLoading: boolean;
  updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => Promise<void>;
  resetSettings: () => Promise<void>;
  applySettings: () => void;
  // Theme-related properties
  currentTheme: typeof lightTheme;
  isDarkMode: boolean;
  systemColorScheme: 'light' | 'dark' | null;
}

const APP_SETTINGS_KEY = 'app_settings_v2';

const DEFAULT_SETTINGS: AppSettings = {
  darkMode: false,
  language: 'English',
  autoSave: true,
  dataSync: true,
  accessibility: false,
  notifications: true,
  soundEnabled: true,
  vibrationEnabled: true,
  fontSize: 'medium',
  theme: 'auto', // Default to auto mode for better UX
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const systemColorScheme = useColorScheme();

  // Calculate current theme based on settings and system preference
  const currentTheme = getAdaptiveColors(settings.theme, systemColorScheme ?? null);
  const isDarkMode = currentTheme === darkTheme;

  useEffect(() => {
    loadSettings();
  }, []);

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    if (settings.theme === 'auto') {
      logger.info(`System theme changed to: ${systemColorScheme}, updating app theme`);
      // The theme will automatically update through getAdaptiveColors
      // No need to save settings as the user preference is still 'auto'
    }
  }, [systemColorScheme, settings.theme]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const stored = await secureStorage.getItem<string>(APP_SETTINGS_KEY);

      if (stored) {
        const parsedSettings = JSON.parse(stored);
        const mergedSettings = { ...DEFAULT_SETTINGS, ...parsedSettings };
        setSettings(mergedSettings);
        logger.info('Settings loaded successfully');
      } else {
        logger.info('No stored settings found, using defaults');
      }
    } catch (error) {
      logger.error('Error loading settings:', error);
      simpleErrorService.reportError(error as Error, {
        component: 'SettingsContext',
        action: 'loadSettings',
        severity: 'medium',
      });
      // Use default settings on error
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: AppSettings) => {
    try {
      await secureStorage.setItem(APP_SETTINGS_KEY, JSON.stringify(newSettings));
      setSettings(newSettings);
      applySettings(newSettings);
      logger.info('Settings saved successfully');
    } catch (error) {
      logger.error('Error saving settings:', error);
      simpleErrorService.reportError(error as Error, {
        component: 'SettingsContext',
        action: 'saveSettings',
        severity: 'high',
      });
      throw error; // Re-throw to let UI handle the error
    }
  };

  const updateSetting = async <K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ): Promise<void> => {
    const newSettings = { ...settings, [key]: value };
    await saveSettings(newSettings);
  };

  const resetSettings = async (): Promise<void> => {
    await saveSettings(DEFAULT_SETTINGS);
  };

  const applySettings = (settingsToApply?: AppSettings) => {
    const currentSettings = settingsToApply || settings;

    try {
      // Apply theme changes
      const newTheme = getAdaptiveColors(currentSettings.theme, systemColorScheme ?? null);
      const newIsDarkMode = newTheme === darkTheme;

      if (newIsDarkMode !== isDarkMode) {
        logger.info(`Theme changed to: ${newIsDarkMode ? 'dark' : 'light'} mode`);
      }

      // Apply accessibility settings
      if (currentSettings.accessibility) {
        logger.info('Accessibility mode enabled');
      }

      // Apply font size
      if (currentSettings.fontSize !== 'medium') {
        logger.info(`Font size set to: ${currentSettings.fontSize}`);
      }

      // Apply language
      if (currentSettings.language !== 'English') {
        logger.info(`Language set to: ${currentSettings.language}`);
      }

      // Apply notification settings
      if (!currentSettings.notifications) {
        logger.info('Notifications disabled');
      }

      logger.info('Settings applied successfully');
    } catch (error) {
      logger.error('Error applying settings:', error);
      simpleErrorService.reportError(error as Error, {
        component: 'SettingsContext',
        action: 'applySettings',
        severity: 'medium',
      });
    }
  };

  // Apply settings when they change
  useEffect(() => {
    if (!isLoading) {
      applySettings();
    }
  }, [settings, isLoading]);

  const value: SettingsContextType = {
    settings,
    isLoading,
    updateSetting,
    resetSettings,
    applySettings: () => applySettings(),
    // Theme-related properties
    currentTheme,
    isDarkMode,
    systemColorScheme: systemColorScheme ?? null,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;
