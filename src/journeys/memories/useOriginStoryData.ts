import { useCallback, useEffect, useState } from 'react';
import { useCoupleRealtime } from '../../shared/services/realtime/useCoupleRealtime';
import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';
import { secureStorage } from '../../shared/utils/secureStorage';
import { useAuth } from '../onboarding/useAuth';
import { useCouplePairing } from '../onboarding/useCouplePairing';

const ORIGIN_STORY_STORAGE_KEY = 'origin_story_data';

export interface StoryPhoto {
  id: string;
  uri: string;
  timestamp: number;
  caption?: string;
}

export interface OriginStoryData {
  firstMeeting: string;
  knewILovedYou: string;
  firstKiss: string;
  insideJokes: string;
  mostRomantic: string;
  biggestChallenge: string;
  bestMemories: string;
  firstMeetingDate?: number;
  knewILovedYouDate?: number;
  firstKissDate?: number;
  insideJokesDate?: number;
  mostRomanticDate?: number;
  biggestChallengeDate?: number;
  bestMemoriesDate?: number;
  firstMeetingPhotos: StoryPhoto[];
  knewILovedYouPhotos: StoryPhoto[];
  firstKissPhotos: StoryPhoto[];
  insideJokesPhotos: StoryPhoto[];
  mostRomanticPhotos: StoryPhoto[];
  biggestChallengePhotos: StoryPhoto[];
  bestMemoriesPhotos: StoryPhoto[];
  completedSections: string[];
  lastUpdated: number;

  // Couple-specific metadata
  couple_id?: string;
  last_updated_by?: string;
}

const DEFAULT_DATA: OriginStoryData = {
  firstMeeting: '',
  knewILovedYou: '',
  firstKiss: '',
  insideJokes: '',
  mostRomantic: '',
  biggestChallenge: '',
  bestMemories: '',
  firstMeetingDate: undefined,
  knewILovedYouDate: undefined,
  firstKissDate: undefined,
  insideJokesDate: undefined,
  mostRomanticDate: undefined,
  biggestChallengeDate: undefined,
  bestMemoriesDate: undefined,
  firstMeetingPhotos: [],
  knewILovedYouPhotos: [],
  firstKissPhotos: [],
  insideJokesPhotos: [],
  mostRomanticPhotos: [],
  biggestChallengePhotos: [],
  bestMemoriesPhotos: [],
  completedSections: [],
  lastUpdated: Date.now(),
};

export function useOriginStoryData() {
  const { user, isAuthenticated } = useAuth();
  const { couple, isPartnerConnected } = useCouplePairing();
  const [data, setData] = useState<OriginStoryData>(DEFAULT_DATA);
  const [isLoading, setIsLoading] = useState(true);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);

      // If user has an active couple, load from couple_stories table
      if (couple && couple.status === 'active' && isAuthenticated) {
        const { data: coupleStory, error } = await supabase
          .from('couple_stories')
          .select('story_data, completed_sections, last_updated_by')
          .eq('couple_id', couple.id)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
          logger.error('Error loading couple story:', error);
        } else if (coupleStory) {
          const storyData = coupleStory.story_data as Partial<OriginStoryData>;
          setData({
            ...DEFAULT_DATA,
            ...storyData,
            completedSections: coupleStory.completed_sections || [],
            couple_id: couple.id,
            last_updated_by: coupleStory.last_updated_by || undefined
          });
        }
      } else {
        // Fall back to local storage for individual users
        const storedData = await secureStorage.getItem(ORIGIN_STORY_STORAGE_KEY);
        if (storedData && typeof storedData === 'string') {
          const parsedData = JSON.parse(storedData);
          setData({ ...DEFAULT_DATA, ...parsedData });
        }
      }
    } catch (error) {
      logger.error('Error loading origin story data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [couple?.id, isAuthenticated]);

  // Initial load and realtime refresh on couple changes
  useEffect(() => {
    loadData();
  }, [loadData]);

  useCoupleRealtime(couple?.id, () => {
    logger.debug('Realtime: origin story changed, refreshing');
    loadData();
  });

  const updateField = async (field: keyof OriginStoryData, value: string) => {
    try {
      const newData = {
        ...data,
        [field]: value,
        lastUpdated: Date.now(),
      };

      // Set completion date when a field is filled for the first time
      const dateField = `${field}Date` as keyof OriginStoryData;
      if (value.trim() !== '' && !data[dateField]) {
        (newData as any)[dateField] = Date.now();
      }

      setData(newData);
      await saveData(newData);
    } catch (error) {
      logger.error('Error updating origin story field:', error);
      throw error;
    }
  };

  const addPhoto = async (section: keyof OriginStoryData, photo: StoryPhoto) => {
    try {
      const photoArray = data[section] as StoryPhoto[];
      if (Array.isArray(photoArray)) {
        const newPhotos = [...photoArray, photo];
        const newData = {
          ...data,
          [section]: newPhotos,
          lastUpdated: Date.now(),
        };
        setData(newData);
        await saveData(newData);
      }
    } catch (error) {
      logger.error('Error adding photo to origin story:', error);
      throw error;
    }
  };

  const removePhoto = async (section: keyof OriginStoryData, photoId: string) => {
    try {
      const photoArray = data[section] as StoryPhoto[];
      if (Array.isArray(photoArray)) {
        const newPhotos = photoArray.filter(photo => photo.id !== photoId);
        const newData = {
          ...data,
          [section]: newPhotos,
          lastUpdated: Date.now(),
        };
        setData(newData);
        await saveData(newData);
      }
    } catch (error) {
      logger.error('Error removing photo from origin story:', error);
      throw error;
    }
  };

  const updateCompletedSections = async (sections: string[]) => {
    try {
      const newData = {
        ...data,
        completedSections: sections,
        lastUpdated: Date.now(),
      };
      setData(newData);
      await saveData(newData);
    } catch (error) {
      logger.error('Error updating completed sections:', error);
      throw error;
    }
  };

  const saveData = async (dataToSave: OriginStoryData) => {
    try {
      // Save to couple storage if available, otherwise local storage
      if (couple && couple.status === 'active' && isAuthenticated) {
        const { error } = await supabase
          .from('couple_stories')
          .upsert({
            couple_id: couple.id,
            story_data: dataToSave as any,
            completed_sections: dataToSave.completedSections,
            last_updated_by: user?.id
          });

        if (error) {
          logger.error('Error saving couple story:', error);
          // Fall back to local storage on error
          await secureStorage.setItem(ORIGIN_STORY_STORAGE_KEY, JSON.stringify(dataToSave));
        } else {
          logger.info('Couple story data saved successfully');
        }
      } else {
        await secureStorage.setItem(ORIGIN_STORY_STORAGE_KEY, JSON.stringify(dataToSave));
        logger.info('Origin story data saved to local storage');
      }
    } catch (error) {
      logger.error('Error saving origin story data:', error);
      throw error;
    }
  };

  const resetData = async () => {
    try {
      await secureStorage.removeItem(ORIGIN_STORY_STORAGE_KEY);
      setData(DEFAULT_DATA);
      logger.info('Origin story data reset successfully');
    } catch (error) {
      logger.error('Error resetting origin story data:', error);
      throw error;
    }
  };

  const getCompletionPercentage = (): number => {
    const totalFields = 7; // firstMeeting, knewILovedYou, firstKiss, insideJokes, mostRomantic, biggestChallenge, bestMemories
    const completedFields = Object.entries(data).filter(([key, value]) => {
      if (key === 'completedSections' || key === 'lastUpdated' || key.includes('Photos')) return false;
      return value && typeof value === 'string' && value.trim() !== '';
    }).length;

    return Math.round((completedFields / totalFields) * 100);
  };

  const isComplete = (): boolean => {
    return getCompletionPercentage() === 100;
  };

  return {
    data,
    isLoading,
    updateField,
    addPhoto,
    removePhoto,
    updateCompletedSections,
    saveData,
    resetData,
    getCompletionPercentage,
    isComplete,
  };
}
