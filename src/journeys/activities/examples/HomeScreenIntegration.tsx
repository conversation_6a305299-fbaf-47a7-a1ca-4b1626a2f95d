/**
 * Home Screen Integration Example
 * Demonstrates how to embed activities on any screen using UniversalActivityContainer
 */

import { router } from 'expo-router';
import { Play, Star, TrendingUp } from 'lucide-react-native';
import React, { memo, useCallback, useState } from 'react';
import {
    Dimensions,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import { colors } from '../../../shared/utils/colors';
import UniversalActivityContainer from '../containers/UniversalActivityContainer';

const { width } = Dimensions.get('window');

interface HomeScreenIntegrationProps {
  userId: string;
  coupleId: string;
  onActivityComplete?: (result: any) => void;
  onActivityError?: (error: any) => void;
}

const HomeScreenIntegration = memo<HomeScreenIntegrationProps>(({
  userId,
  coupleId,
  onActivityComplete,
  onActivityError
}) => {
  const [featuredActivity, setFeaturedActivity] = useState<string | null>('match-activity');
  const [quickActivities] = useState([
    { id: 'daily-questions', title: 'Daily Questions', points: 5 },
    { id: 'would-you-rather', title: 'Would You Rather', points: 10 },
    { id: 'love-language-quiz', title: 'Love Language Quiz', points: 15 },
  ]);

  const handleActivityComplete = useCallback((result: any) => {
    console.log('Home screen activity completed:', result);
    onActivityComplete?.(result);

    // Show success feedback
    // You could add a toast notification here
  }, [onActivityComplete]);

  const handleActivityError = useCallback((error: any) => {
    console.error('Home screen activity error:', error);
    onActivityError?.(error);

    // Show error feedback
    // You could add an error toast here
  }, [onActivityError]);

  const handleQuickActivityPress = useCallback((activityId: string) => {
    // Navigate to full activity screen
    router.push(`/activities/${activityId}` as any);
  }, []);

  const handleViewAllActivities = useCallback(() => {
    router.push('/activities' as any);
  }, []);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Featured Activity Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Featured Activity</Text>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={handleViewAllActivities}
            activeOpacity={0.7}
            accessibilityLabel="View all activities"
            accessibilityRole="button"
          >
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        {featuredActivity && (
          <View style={styles.featuredActivityContainer}>
            <UniversalActivityContainer
              activityId={featuredActivity}
              userId={userId}
              coupleId={coupleId}
              onComplete={handleActivityComplete}
              onError={handleActivityError}
              showHeader={false}
              showProgress={true}
              showExitButton={true}
            />
          </View>
        )}
      </View>

      {/* Quick Activities Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Quick Activities</Text>
          <Star size={20} color={colors.accent} />
        </View>

        <View style={styles.quickActivitiesGrid}>
          {quickActivities.map((activity) => (
            <TouchableOpacity
              key={activity.id}
              style={styles.quickActivityCard}
              onPress={() => handleQuickActivityPress(activity.id)}
              activeOpacity={0.7}
              accessibilityLabel={`Start ${activity.title} activity`}
              accessibilityRole="button"
            >
              <View style={styles.quickActivityContent}>
                <Play size={24} color={colors.primary} />
                <Text style={styles.quickActivityTitle}>{activity.title}</Text>
                <View style={styles.pointsContainer}>
                  <TrendingUp size={16} color={colors.accent} />
                  <Text style={styles.pointsText}>{activity.points} pts</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Activity Stats Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Your Progress</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Activities Completed</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>150</Text>
            <Text style={styles.statLabel}>Points Earned</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>7</Text>
            <Text style={styles.statLabel}>Day Streak</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
});

HomeScreenIntegration.displayName = 'HomeScreenIntegration';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  viewAllButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  featuredActivityContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  quickActivitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActivityCard: {
    width: (width - 48) / 2,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  quickActivityContent: {
    alignItems: 'center',
  },
  quickActivityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 8,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  pointsText: {
    fontSize: 12,
    color: colors.accent,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default HomeScreenIntegration;
