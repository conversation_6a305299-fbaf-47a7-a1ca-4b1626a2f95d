import React, { useState } from 'react';
import {
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import {
    Check,
    Filter,
    Search,
    X
} from 'lucide-react-native';
import {
    CostLevel,
    DifficultyLevel,
    LocationType
} from '../../types/supabase.types';
import { colors } from '../../utils/colors';

// Define DateNightFilters type
export interface DateNightFilters {
  cost?: CostLevel[];
  difficulty?: DifficultyLevel[];
  location?: LocationType[];
  category?: string[];
}

interface FiltersProps {
  filters: DateNightFilters;
  onFiltersChange: (filters: DateNightFilters) => void;
  categories: string[];
  isLoading?: boolean;
}

const COST_OPTIONS: { value: CostLevel; label: string; color: string }[] = [
  { value: 'free', label: 'Free', color: colors.success },
  { value: 'low', label: 'Low Cost', color: colors.warning },
  { value: 'medium', label: 'Medium Cost', color: colors.primary },
  { value: 'high', label: 'High Cost', color: colors.error },
];

const DIFFICULTY_OPTIONS: { value: DifficultyLevel; label: string; color: string }[] = [
  { value: 'easy', label: 'Easy', color: colors.success },
  { value: 'medium', label: 'Medium', color: colors.warning },
  { value: 'hard', label: 'Hard', color: colors.error },
];

const LOCATION_OPTIONS: { value: LocationType; label: string; icon: string }[] = [
  { value: 'indoor', label: 'Indoor', icon: '🏠' },
  { value: 'outdoor', label: 'Outdoor', icon: '🌳' },
  { value: 'both', label: 'Both', icon: '🏠🌳' },
];

export default function Filters({
  filters,
  onFiltersChange,
  categories,
  isLoading = false
}: FiltersProps) {
  const [showModal, setShowModal] = useState(false);
  const [tempFilters, setTempFilters] = useState<DateNightFilters>(filters);

  const handleApplyFilters = () => {
    onFiltersChange(tempFilters);
    setShowModal(false);
  };

  const handleClearFilters = () => {
    const clearedFilters: DateNightFilters = {};
    setTempFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    setShowModal(false);
  };

  const handleSearchChange = (search: string) => {
    const newFilters = { ...tempFilters, search: search.trim() || undefined };
    setTempFilters(newFilters);
  };

  const toggleFilter = (key: keyof DateNightFilters, value: any) => {
    const currentValue = tempFilters[key];
    const newFilters = {
      ...tempFilters,
      [key]: currentValue === value ? undefined : value
    };
    setTempFilters(newFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  const renderFilterSection = (
    title: string,
    children: React.ReactNode
  ) => (
    <View style={styles.filterSection}>
      <Text style={styles.filterSectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderOptionButton = (
    value: any,
    label: string,
    isSelected: boolean,
    color?: string,
    icon?: string
  ) => (
    <TouchableOpacity
      key={value}
      style={[
        styles.optionButton,
        isSelected && styles.optionButtonSelected,
        color && isSelected && { backgroundColor: color }
      ]}
      onPress={() => toggleFilter(value as keyof DateNightFilters, value)}
    >
      <Text style={[
        styles.optionButtonText,
        isSelected && styles.optionButtonTextSelected
      ]}>
        {icon && `${icon} `}{label}
      </Text>
      {isSelected && (
        <Check size={16} color={colors.white} />
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => setShowModal(true)}
        disabled={isLoading}
      >
        <Filter size={20} color={colors.primary} />
        {getActiveFiltersCount() > 0 && (
          <View style={styles.filterBadge}>
            <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
          </View>
        )}
      </TouchableOpacity>

      <Modal
        visible={showModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Date Nights</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {/* Search */}
              {renderFilterSection(
                'Search',
                <View style={styles.searchContainer}>
                  <Search size={20} color={colors.textTertiary} style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search ideas..."
                    value={tempFilters.search || ''}
                    onChangeText={handleSearchChange}
                    placeholderTextColor={colors.textTertiary}
                  />
                </View>
              )}

              {/* Categories */}
              {renderFilterSection(
                'Categories',
                <View style={styles.optionsGrid}>
                  {categories.map(category =>
                    renderOptionButton(
                      category,
                      category,
                      tempFilters.category === category
                    )
                  )}
                </View>
              )}

              {/* Cost Level */}
              {renderFilterSection(
                'Cost Level',
                <View style={styles.optionsGrid}>
                  {COST_OPTIONS.map(option =>
                    renderOptionButton(
                      option.value,
                      option.label,
                      tempFilters.cost === option.value,
                      option.color
                    )
                  )}
                </View>
              )}

              {/* Difficulty */}
              {renderFilterSection(
                'Difficulty',
                <View style={styles.optionsGrid}>
                  {DIFFICULTY_OPTIONS.map(option =>
                    renderOptionButton(
                      option.value,
                      option.label,
                      tempFilters.difficulty === option.value,
                      option.color
                    )
                  )}
                </View>
              )}

              {/* Location */}
              {renderFilterSection(
                'Location',
                <View style={styles.optionsGrid}>
                  {LOCATION_OPTIONS.map(option =>
                    renderOptionButton(
                      option.value,
                      option.label,
                      tempFilters.indoor_outdoor === option.value,
                      undefined,
                      option.icon
                    )
                  )}
                </View>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClearFilters}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.applyButton}
                onPress={handleApplyFilters}
              >
                <View
                  style={[styles.applyButtonGradient, { backgroundColor: colors.primary }]}
                >
                  <Text style={styles.applyButtonText}>Apply Filters</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  filterButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
    position: 'relative',
  },
  filterBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  modalBody: {
    padding: 20,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.borderLight,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  optionButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
  },
  optionButtonTextSelected: {
    color: colors.white,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  clearButton: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  applyButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  applyButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  applyButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
