/**
 * Match Activity Component
 * Portable, standalone component that can be rendered on any screen
 * Preserves exact same functionality as the original SimpleActivityInterface
 */

import { ArrowRight } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    Alert,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { colors } from '../../../../utils/colors';
import { useUserProfile } from '../../useUserProfile';

// Preserve exact same interfaces from Activities.tsx
export interface DualEntryPrompt {
  id: string;
  question: string;
  type: 'text' | 'list' | 'guess';
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface ActivityResponse {
  promptId: string;
  partner1Answer: string;
  partner2Guess: string;
  timestamp: number;
  isRevealed: boolean;
  isMatch: boolean;
  category?: string;
}

export interface RelationshipActivity {
  id: string;
  title: string;
  description: string;
  prompts: DualEntryPrompt[];
  responses: ActivityResponse[];
  completedAt?: number;
  theme?: 'default' | 'romantic' | 'playful' | 'reflective';
}

interface MatchActivityProps {
  activityId: string;
  userId: string;
  coupleId: string;
  onComplete: (result: any) => void;
  onExit: () => void;
  onError: (error: any) => void;
  theme?: 'default' | 'romantic' | 'playful' | 'reflective';
  customData?: Record<string, any>;
}

export function MatchActivity({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  theme = 'playful'
}: MatchActivityProps) {
  const { profile, getPartnerNames } = useUserProfile();
  const partnerNames = getPartnerNames();

  // Activity state - matches original SimpleActivityInterface behavior
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0);
  const [activityResponses, setActivityResponses] = useState<ActivityResponse[]>([]);
  const [activityPhase, setActivityPhase] = useState<'answer' | 'guess' | 'reveal'>('answer');

  // Match activity prompts - exact same as original
  const prompts: DualEntryPrompt[] = [
    {
      id: 'match-1',
      question: 'What is your partner\'s favorite type of music?',
      type: 'text',
      category: 'preferences',
      difficulty: 'easy'
    },
    {
      id: 'match-2',
      question: 'What would your partner choose: beach vacation or mountain retreat?',
      type: 'text',
      category: 'preferences',
      difficulty: 'easy'
    },
    {
      id: 'match-3',
      question: 'What is your partner\'s biggest fear?',
      type: 'text',
      category: 'personal',
      difficulty: 'medium'
    },
    {
      id: 'match-4',
      question: 'What is your partner\'s dream job?',
      type: 'text',
      category: 'aspirations',
      difficulty: 'medium'
    },
    {
      id: 'match-5',
      question: 'What is your partner\'s most embarrassing moment?',
      type: 'text',
      category: 'personal',
      difficulty: 'hard'
    }
  ];

  const currentPrompt = prompts[currentPromptIndex];

  // Get theme colors - matches original logic
  const getThemeColors = () => {
    switch (theme) {
      case 'romantic':
        return colors.solidColors.modulePink;
      case 'playful':
        return colors.solidColors.moduleOrange;
      case 'reflective':
        return colors.solidColors.moduleBlue;
      default:
        return colors.primary;
    }
  };

  const themeColors = getThemeColors();

  // Handle answer submit - exact same logic as original
  const handleAnswerSubmit = useCallback((answer: string) => {
    const response: ActivityResponse = {
      promptId: currentPrompt.id,
      partner1Answer: answer,
      partner2Guess: '',
      timestamp: Date.now(),
      isRevealed: false,
      isMatch: false
    };

    setActivityResponses(prev => [...prev, response]);
    setActivityPhase('guess');
  }, [currentPrompt.id]);

  // Handle guess submit - exact same logic as original
  const handleGuessSubmit = useCallback((guess: string) => {
    const currentResponse = activityResponses[activityResponses.length - 1];
    const isMatch = currentResponse.partner1Answer.toLowerCase().trim() === guess.toLowerCase().trim();

    const updatedResponse: ActivityResponse = {
      ...currentResponse,
      partner2Guess: guess,
      isMatch: isMatch
    };

    const updatedResponses = [...activityResponses];
    updatedResponses[updatedResponses.length - 1] = updatedResponse;
    setActivityResponses(updatedResponses);
    setActivityPhase('reveal');
  }, [activityResponses]);

  // Handle reveal complete - exact same logic as original
  const handleRevealComplete = useCallback(() => {
    if (currentPromptIndex < prompts.length - 1) {
      setCurrentPromptIndex(prev => prev + 1);
      setActivityPhase('answer');
    } else {
      completeActivity();
    }
  }, [currentPromptIndex, prompts.length]);

  // Complete activity - exact same logic as original
  const completeActivity = useCallback(() => {
    const score = activityResponses.filter(r => r.isMatch).length * 10;

    const result = {
      activityId,
      userId,
      coupleId,
      score,
      points: 25,
      completedAt: new Date().toISOString(),
      data: {
        responses: activityResponses,
        totalQuestions: prompts.length,
        matchingAnswers: activityResponses.filter(r => r.isMatch).length,
        activityType: 'match-activity'
      }
    };

    Alert.alert(
      'Activity Completed! 🎉',
      `Great job completing The Match Activity! You earned 25 points!`,
      [
        {
          text: 'Play Another Activity',
          onPress: () => {
            onComplete(result);
          }
        },
        {
          text: 'View Scrapbook',
          onPress: () => {
            onComplete(result);
          }
        }
      ]
    );
  }, [activityId, userId, coupleId, activityResponses, prompts.length, onComplete]);

  // Exit activity
  const exitActivity = useCallback(() => {
    onExit();
  }, [onExit]);

  // Render answer phase - exact same as original SimpleActivityInterface
  const renderAnswerPhase = () => (
    <View style={styles.activityContent}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{currentPrompt.question}</Text>
        <Text style={styles.instructionText}>
          {partnerNames[0]}, enter your answer below:
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Type your answer here..."
          multiline
          textAlignVertical="top"
          onSubmitEditing={(e) => {
            if (e.nativeEvent.text.trim()) {
              handleAnswerSubmit(e.nativeEvent.text.trim());
            }
          }}
        />
      </View>

      <TouchableOpacity
        style={[styles.submitButton, { backgroundColor: themeColors[1] }]}
        onPress={() => {
          // For demo purposes, simulate an answer
          handleAnswerSubmit('Sample answer');
        }}
      >
        <Text style={styles.submitButtonText}>Submit Answer</Text>
        <ArrowRight size={20} color={colors.white} />
      </TouchableOpacity>
    </View>
  );

  // Render guess phase - exact same as original SimpleActivityInterface
  const renderGuessPhase = () => (
    <View style={styles.activityContent}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{currentPrompt.question}</Text>
        <Text style={styles.instructionText}>
          {partnerNames[1]}, try to guess {partnerNames[0]}'s answer:
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="What do you think they answered?"
          multiline
          textAlignVertical="top"
          onSubmitEditing={(e) => {
            if (e.nativeEvent.text.trim()) {
              handleGuessSubmit(e.nativeEvent.text.trim());
            }
          }}
        />
      </View>

      <TouchableOpacity
        style={[styles.submitButton, { backgroundColor: themeColors[1] }]}
        onPress={() => {
          // For demo purposes, simulate a guess
          handleGuessSubmit('Sample guess');
        }}
      >
        <Text style={styles.submitButtonText}>Submit Guess</Text>
        <ArrowRight size={20} color={colors.white} />
      </TouchableOpacity>
    </View>
  );

  // Render reveal phase - exact same as original SimpleActivityInterface
  const renderRevealPhase = () => {
    const currentResponse = activityResponses[activityResponses.length - 1];

    return (
      <View style={styles.activityContent}>
        <View style={styles.questionContainer}>
          <Text style={styles.questionText}>{currentPrompt.question}</Text>
        </View>

        <View style={styles.revealContainer}>
          <View style={styles.answerCard}>
            <Text style={styles.answerLabel}>{partnerNames[0]}'s Answer:</Text>
            <Text style={styles.answerText}>{currentResponse?.partner1Answer}</Text>
          </View>

          <View style={styles.answerCard}>
            <Text style={styles.answerLabel}>{partnerNames[1]}'s Guess:</Text>
            <Text style={styles.answerText}>{currentResponse?.partner2Guess}</Text>
          </View>

          <View style={[styles.matchResult, { backgroundColor: currentResponse?.isMatch ? colors.success : colors.error }]}>
            <Text style={styles.matchResultText}>
              {currentResponse?.isMatch ? '🎉 Perfect Match!' : '❌ Not quite...'}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: themeColors[1] }]}
          onPress={handleRevealComplete}
        >
          <Text style={styles.submitButtonText}>
            {currentPromptIndex === prompts.length - 1 ? 'Complete Activity' : 'Next Question'}
          </Text>
          <ArrowRight size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.activityInterface}>
      <View style={styles.activityHeader}>
        <TouchableOpacity style={styles.exitButton} onPress={exitActivity}>
          <Text style={styles.exitButtonText}>Exit Activity</Text>
        </TouchableOpacity>
      </View>

      {activityPhase === 'answer' && renderAnswerPhase()}
      {activityPhase === 'guess' && renderGuessPhase()}
      {activityPhase === 'reveal' && renderRevealPhase()}
    </View>
  );
}

// Preserve exact same styles from Activities.tsx
const styles = StyleSheet.create({
  activityInterface: {
    flex: 1,
    backgroundColor: colors.background,
  },
  activityHeader: {
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
  },
  activityContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  questionContainer: {
    marginBottom: 30,
  },
  questionText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 32,
  },
  instructionText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 30,
  },
  textInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  revealContainer: {
    flex: 1,
    marginBottom: 30,
  },
  answerCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 8,
  },
  answerText: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
  },
  matchResult: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  matchResultText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  exitButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 8,
  },
  exitButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
});
