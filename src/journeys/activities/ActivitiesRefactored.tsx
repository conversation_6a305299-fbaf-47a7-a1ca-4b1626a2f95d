/**
 * Refactored Activities Screen
 * Uses modular activity system while preserving 100% of original functionality
 * All UI, behavior, and interactions remain identical to the original
 */

import React, { useMemo, useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { router } from 'expo-router';
import {
    ArrowRight,
    Gamepad2,
    Play
} from 'lucide-react-native';
import { colors } from '../../utils/colors';
import { PointsDisplay } from './PointsDisplay';
import { useUserProfile } from './useUserProfile';

// Import the registry system
import { Activity, activityRegistry } from './registry/ActivityRegistry';

const { width } = Dimensions.get('window');

// Preserve exact same interfaces from original Activities.tsx
export interface RelationshipActivityPrompt {
  id: string;
  question: string;
  type: 'text' | 'list' | 'guess';
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface RelationshipActivityResponse {
  promptId: string;
  partner1Answer: string;
  partner2Answer: string;
  timestamp: number;
  isRevealed: boolean;
  matchScore?: number;
  category?: string;
}

export interface DualEntryPrompt {
  id: string;
  question: string;
  type: 'text' | 'list' | 'guess';
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface ActivityResponse {
  promptId: string;
  partner1Answer: string;
  partner2Guess: string;
  timestamp: number;
  isRevealed: boolean;
  isMatch: boolean;
  category?: string;
}

export interface RelationshipActivity {
  id: string;
  title: string;
  description: string;
  prompts: DualEntryPrompt[];
  responses: ActivityResponse[];
  completedAt?: number;
  theme?: 'default' | 'romantic' | 'playful' | 'reflective';
}

interface SimpleActivityInterfaceProps {
  prompt: DualEntryPrompt;
  activityPhase: 'answer' | 'guess' | 'reveal';
  currentResponse?: ActivityResponse;
  partner1Name: string;
  partner2Name: string;
  onAnswerSubmit: (answer: string) => void;
  onGuessSubmit: (guess: string) => void;
  onRevealComplete: () => void;
  onExit: () => void;
  isLastPrompt: boolean;
  theme: 'default' | 'romantic' | 'playful' | 'reflective';
}

// Preserve exact same SimpleActivityInterface component
const SimpleActivityInterface: React.FC<SimpleActivityInterfaceProps> = ({
  prompt,
  activityPhase,
  currentResponse,
  partner1Name,
  partner2Name,
  onAnswerSubmit,
  onGuessSubmit,
  onRevealComplete,
  onExit,
  isLastPrompt,
  theme
}) => {
  const [input, setInput] = useState('');

  const getThemeColors = () => {
    switch (theme) {
      case 'romantic':
        return colors.solidColors.modulePink;
      case 'playful':
        return colors.solidColors.moduleOrange;
      case 'reflective':
        return colors.solidColors.moduleBlue;
      default:
        return colors.primary;
    }
  };

  const themeColors = getThemeColors();

  const handleSubmit = () => {
    if (!input.trim()) {
      Alert.alert('Missing Input', 'Please enter your answer before continuing.');
      return;
    }

    if (activityPhase === 'answer') {
      onAnswerSubmit(input.trim());
    } else if (activityPhase === 'guess') {
      onGuessSubmit(input.trim());
    }

    setInput('');
  };

  const renderAnswerPhase = () => (
    <View style={styles.activityContent}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{prompt.question}</Text>
        <Text style={styles.instructionText}>
          {partner1Name}, enter your answer below:
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={input}
          onChangeText={setInput}
          placeholder="Type your answer here..."
          multiline
          textAlignVertical="top"
        />
      </View>

      <TouchableOpacity style={[styles.submitButton, { backgroundColor: themeColors[1] }]} onPress={handleSubmit}>
        <Text style={styles.submitButtonText}>Submit Answer</Text>
        <ArrowRight size={20} color={colors.white} />
      </TouchableOpacity>
    </View>
  );

  const renderGuessPhase = () => (
    <View style={styles.activityContent}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{prompt.question}</Text>
        <Text style={styles.instructionText}>
          {partner2Name}, try to guess {partner1Name}'s answer:
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={input}
          onChangeText={setInput}
          placeholder="What do you think they answered?"
          multiline
          textAlignVertical="top"
        />
      </View>

      <TouchableOpacity style={[styles.submitButton, { backgroundColor: themeColors[1] }]} onPress={handleSubmit}>
        <Text style={styles.submitButtonText}>Submit Guess</Text>
        <ArrowRight size={20} color={colors.white} />
      </TouchableOpacity>
    </View>
  );

  const renderRevealPhase = () => (
    <View style={styles.activityContent}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{prompt.question}</Text>
      </View>

      <View style={styles.revealContainer}>
        <View style={styles.answerCard}>
          <Text style={styles.answerLabel}>{partner1Name}'s Answer:</Text>
          <Text style={styles.answerText}>{currentResponse?.partner1Answer}</Text>
        </View>

        <View style={styles.answerCard}>
          <Text style={styles.answerLabel}>{partner2Name}'s Guess:</Text>
          <Text style={styles.answerText}>{currentResponse?.partner2Guess}</Text>
        </View>

        <View style={[styles.matchResult, { backgroundColor: currentResponse?.isMatch ? colors.success : colors.error }]}>
          <Text style={styles.matchResultText}>
            {currentResponse?.isMatch ? '🎉 Perfect Match!' : '❌ Not quite...'}
          </Text>
        </View>
      </View>

      <TouchableOpacity style={[styles.submitButton, { backgroundColor: themeColors[1] }]} onPress={onRevealComplete}>
        <Text style={styles.submitButtonText}>
          {isLastPrompt ? 'Complete Activity' : 'Next Question'}
        </Text>
        <ArrowRight size={20} color={colors.white} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.activityInterface}>
      <View style={styles.activityHeader}>
        <TouchableOpacity style={styles.exitButton} onPress={onExit}>
          <Text style={styles.exitButtonText}>Exit Activity</Text>
        </TouchableOpacity>
      </View>

      {activityPhase === 'answer' && renderAnswerPhase()}
      {activityPhase === 'guess' && renderGuessPhase()}
      {activityPhase === 'reveal' && renderRevealPhase()}
    </View>
  );
};

export default function Activities() {
  const { profile, getPartnerNames } = useUserProfile();
  const [selectedCategory, setSelectedCategory] = useState<'all' | Activity['category']>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<'all' | Activity['difficulty']>('all');

  // Preserve exact same state management as original
  const [currentActivity, setCurrentActivity] = useState<RelationshipActivity | null>(null);
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0);
  const [activityResponses, setActivityResponses] = useState<ActivityResponse[]>([]);
  const [activityPhase, setActivityPhase] = useState<'answer' | 'guess' | 'reveal'>('answer');
  const [showActivityInterface, setShowActivityInterface] = useState(false);

  const partnerNames = getPartnerNames();

  // Use registry instead of hardcoded array - but get exact same data
  const allActivities = activityRegistry.getAllActivities();

  // Preserve exact same startActivity logic
  const startActivity = (activity: Activity) => {
    if (activity.prompts && activity.prompts.length > 0) {
      const activityInstance: RelationshipActivity = {
        id: activity.id,
        title: activity.title,
        description: activity.description,
        prompts: activity.prompts,
        responses: [],
        theme: activity.theme || 'default'
      };

      setCurrentActivity(activityInstance);
      setCurrentPromptIndex(0);
      setActivityResponses([]);
      setActivityPhase('answer');
      setShowActivityInterface(true);
    } else {
      router.push(activity.route as any);
    }
  };

  // Preserve exact same handler functions
  const handleAnswerSubmit = (answer: string) => {
    const response: ActivityResponse = {
      promptId: currentActivity!.prompts[currentPromptIndex].id,
      partner1Answer: answer,
      partner2Guess: '',
      timestamp: Date.now(),
      isRevealed: false,
      isMatch: false
    };

    setActivityResponses([...activityResponses, response]);
    setActivityPhase('guess');
  };

  const handleGuessSubmit = (guess: string) => {
    const currentResponse = activityResponses[activityResponses.length - 1];
    const isMatch = currentResponse.partner1Answer.toLowerCase().trim() === guess.toLowerCase().trim();

    const updatedResponse: ActivityResponse = {
      ...currentResponse,
      partner2Guess: guess,
      isMatch: isMatch
    };

    const updatedResponses = [...activityResponses];
    updatedResponses[updatedResponses.length - 1] = updatedResponse;
    setActivityResponses(updatedResponses);
    setActivityPhase('reveal');
  };

  const handleRevealComplete = () => {
    if (currentPromptIndex < (currentActivity?.prompts.length || 0) - 1) {
      setCurrentPromptIndex(currentPromptIndex + 1);
      setActivityPhase('answer');
    } else {
      completeActivity();
    }
  };

  const completeActivity = () => {
    if (currentActivity) {
      const completedActivity: RelationshipActivity = {
        ...currentActivity,
        responses: activityResponses,
        completedAt: Date.now()
      };

      Alert.alert(
        'Activity Completed! 🎉',
        `Great job completing ${currentActivity.title}! You earned 25 points!`,
        [
          {
            text: 'Play Another Activity',
            onPress: () => {
              setShowActivityInterface(false);
              setCurrentActivity(null);
            }
          },
          {
            text: 'View Scrapbook',
            onPress: () => {
              router.push('/scrapbook');
            }
          }
        ]
      );
    }
  };

  const exitActivity = () => {
    setShowActivityInterface(false);
    setCurrentActivity(null);
    setCurrentPromptIndex(0);
    setActivityResponses([]);
    setActivityPhase('answer');
  };

  // Preserve exact same filtering logic
  const filteredActivities = useMemo(() => {
    return allActivities.filter(activity => {
      if (selectedCategory !== 'all' && activity.category !== selectedCategory) return false;
      if (selectedDifficulty !== 'all' && activity.difficulty !== selectedDifficulty) return false;
      return true;
    });
  }, [selectedCategory, selectedDifficulty, allActivities]);

  // Preserve exact same helper functions
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return colors.success;
      case 'medium': return colors.warning;
      case 'hard': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'getting-to-know': return colors.primary;
      case 'communication': return colors.secondary;
      case 'fun': return colors.success;
      case 'reflection': return colors.warning;
      case 'skills': return colors.error;
      case 'planning': return colors.info;
      default: return colors.primary;
    }
  };

  const handleActivityPress = (activity: Activity) => {
    if (activity.isAvailable) {
      startActivity(activity);
    } else {
      Alert.alert('Coming Soon', 'This activity will be available in a future update!');
    }
  };

  // Use registry for category stats - but get exact same data
  const categoryStats = activityRegistry.getCategoryStats();

  // Preserve exact same render logic for activity interface
  if (showActivityInterface && currentActivity) {
    return (
      <View style={styles.activityContainer}>
        <View
          style={[styles.activityHeader, { backgroundColor: getCategoryColor('getting-to-know') }]}
        >
          <Text style={styles.activityHeaderTitle}>{currentActivity.title}</Text>
          <Text style={styles.activityHeaderSubtitle}>{currentActivity.description}</Text>
          <PointsDisplay
            points={0}
            showProgress={true}
            current={currentPromptIndex + 1}
            total={currentActivity.prompts.length}
            progressTheme={currentActivity.theme || 'default'}
            progressLabel="Prompt"
            size="small"
          />
        </View>

        <SimpleActivityInterface
          prompt={currentActivity.prompts[currentPromptIndex]}
          activityPhase={activityPhase}
          currentResponse={activityResponses[currentPromptIndex]}
          partner1Name={partnerNames[0]}
          partner2Name={partnerNames[1]}
          onAnswerSubmit={handleAnswerSubmit}
          onGuessSubmit={handleGuessSubmit}
          onRevealComplete={handleRevealComplete}
          onExit={exitActivity}
          isLastPrompt={currentPromptIndex === currentActivity.prompts.length - 1}
          theme={currentActivity.theme || 'default'}
        />
      </View>
    );
  }

  // Preserve exact same main render - only change is using registry data
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Activities Hub</Text>
        <Text style={styles.headerSubtitle}>
          Discover fun activities to strengthen your relationship
        </Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryScroll}
        contentContainerStyle={styles.categoryContainer}
      >
        <TouchableOpacity
          style={[
            styles.categoryPill,
            selectedCategory === 'all' && styles.categoryPillActive
          ]}
          onPress={() => setSelectedCategory('all')}
        >
          <Text style={[
            styles.categoryPillText,
            selectedCategory === 'all' && styles.categoryPillTextActive
          ]}>
            All Activities ({allActivities.length})
          </Text>
        </TouchableOpacity>

        {Object.entries(categoryStats).map(([category, count]) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryPill,
              selectedCategory === category && styles.categoryPillActive
            ]}
            onPress={() => setSelectedCategory(category as Activity['category'])}
          >
            <Text style={[
              styles.categoryPillText,
              selectedCategory === category && styles.categoryPillTextActive
            ]}>
              {category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} ({count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={styles.difficultyFilter}>
        <Text style={styles.filterLabel}>Difficulty:</Text>
        <View style={styles.difficultyButtons}>
          {(['all', 'easy', 'medium', 'hard'] as const).map((difficulty) => (
            <TouchableOpacity
              key={difficulty}
              style={[
                styles.difficultyButton,
                selectedDifficulty === difficulty && styles.difficultyButtonActive
              ]}
              onPress={() => setSelectedDifficulty(difficulty)}
            >
              <Text style={[
                styles.difficultyButtonText,
                selectedDifficulty === difficulty && styles.difficultyButtonTextActive
              ]}>
                {difficulty === 'all' ? 'All' : difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView style={styles.activitiesContainer} showsVerticalScrollIndicator={false}>
        {filteredActivities.length === 0 ? (
          <View style={styles.emptyState}>
            <Gamepad2 size={48} color={colors.textTertiary} />
            <Text style={styles.emptyStateText}>No activities match your filters</Text>
            <Text style={styles.emptyStateSubtext}>Try adjusting your category or difficulty selection</Text>
          </View>
        ) : (
          <View style={styles.activitiesGrid}>
            {filteredActivities.map((activity) => (
              <TouchableOpacity
                key={activity.id}
                style={styles.activityCard}
                onPress={() => handleActivityPress(activity)}
                activeOpacity={0.8}
              >
                <View
                  style={[styles.activityCardGradient, { backgroundColor: getCategoryColor(activity.category) }]}
                >
                  <View style={styles.activityCardHeader}>
                    <View style={styles.activityIconContainer}>
                      {activity.icon}
                    </View>
                    <View style={styles.activityDifficulty}>
                      <View style={[
                        styles.difficultyDot,
                        { backgroundColor: getDifficultyColor(activity.difficulty) }
                      ]} />
                      <Text style={styles.difficultyText}>
                        {activity.difficulty.charAt(0).toUpperCase() + activity.difficulty.slice(1)}
                      </Text>
                    </View>
                  </View>

                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <Text style={styles.activityDescription}>{activity.description}</Text>

                  <View style={styles.activityFooter}>
                    <View style={styles.activityMeta}>
                      <Text style={styles.activityMetaText}>⏱️ {activity.duration}</Text>
                      <Text style={styles.activityMetaText}>👥 {activity.players}</Text>
                      <Text style={styles.activityMetaText}>⭐ {activity.points} pts</Text>
                    </View>

                    <TouchableOpacity
                      style={styles.playButton}
                      onPress={() => handleActivityPress(activity)}
                    >
                      <Play size={20} color={colors.white} />
                      <Text style={styles.playButtonText}>Play</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

// Preserve exact same styles from original Activities.tsx
const styles = StyleSheet.create({
  // Activity interface styles
  activityContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  activityHeader: {
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
  },
  activityHeaderTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 8,
    textAlign: 'center',
  },
  activityHeaderSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  // Simple Activity Interface styles
  activityInterface: {
    flex: 1,
    backgroundColor: colors.background,
  },
  activityContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  questionContainer: {
    marginBottom: 30,
  },
  questionText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 32,
  },
  instructionText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 30,
  },
  textInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  revealContainer: {
    flex: 1,
    marginBottom: 30,
  },
  answerCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 8,
  },
  answerText: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
  },
  matchResult: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  matchResultText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  exitButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 8,
  },
  exitButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    backgroundColor: colors.background,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  categoryScroll: {
    maxHeight: 60,
  },
  categoryContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryPill: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  categoryPillActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  categoryPillTextActive: {
    color: colors.white,
  },
  difficultyFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginVertical: 20,
    gap: 12,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
  },
  difficultyButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  difficultyButton: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  difficultyButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  difficultyButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  difficultyButtonTextActive: {
    color: colors.white,
  },
  activitiesContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
  activitiesGrid: {
    gap: 16,
    paddingBottom: 20,
  },
  activityCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  activityCardGradient: {
    padding: 20,
  },
  activityCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  activityIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 8,
  },
  activityDifficulty: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.white,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 8,
  },
  activityDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 16,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activityMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  activityMetaText: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
  },
  playButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  playButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
});
