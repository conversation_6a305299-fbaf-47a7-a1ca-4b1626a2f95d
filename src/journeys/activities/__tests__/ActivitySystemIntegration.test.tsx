/**
 * Activity System Integration Tests
 * Tests the complete activity system integration across multiple screens
 */

import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';
import HomeScreenIntegration from '../examples/HomeScreenIntegration';
import ProfileScreenIntegration from '../examples/ProfileScreenIntegration';

// Mock all dependencies
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
  },
}));

jest.mock('../containers/UniversalActivityContainer', () => ({
  __esModule: true,
  default: ({ activityId, onComplete, onError }: any) => (
    <div testID={`activity-${activityId}`}>
      <div testID="activity-content">Activity: {activityId}</div>
      <button testID="complete-activity" onPress={() => onComplete({ score: 90, points: 15 })}>
        Complete Activity
      </button>
      <button testID="error-activity" onPress={() => onError(new Error('Test error'))}>
        Trigger Error
      </button>
    </div>
  ),
}));

jest.mock('../../../shared/utils/colors', () => ({
  colors: {
    primary: '#007AFF',
    background: '#FFFFFF',
    surface: '#F8F9FA',
    textPrimary: '#000000',
    textSecondary: '#666666',
    accent: '#FF9500',
    error: '#FF3B30',
    white: '#FFFFFF',
    shadow: '#000000',
  },
}));

describe('Activity System Integration', () => {
  const mockUser = {
    id: 'user-123',
    name: 'John Doe',
    totalPoints: 150,
    activitiesCompleted: 12,
    currentStreak: 7,
  };

  const mockCouple = {
    id: 'couple-456',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Home Screen Integration', () => {
    it('should render home screen with featured activity', () => {
      const { getByText, getByTestId } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      expect(getByText('Featured Activity')).toBeTruthy();
      expect(getByTestId('activity-match-activity')).toBeTruthy();
    });

    it('should render quick activities section', () => {
      const { getByText } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      expect(getByText('Quick Activities')).toBeTruthy();
      expect(getByText('Daily Questions')).toBeTruthy();
      expect(getByText('Would You Rather')).toBeTruthy();
      expect(getByText('Love Language Quiz')).toBeTruthy();
    });

    it('should render progress stats', () => {
      const { getByText } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      expect(getByText('Your Progress')).toBeTruthy();
      expect(getByText('12')).toBeTruthy(); // Activities Completed
      expect(getByText('150')).toBeTruthy(); // Points Earned
      expect(getByText('7')).toBeTruthy(); // Day Streak
    });

    it('should handle activity completion', async () => {
      const onComplete = jest.fn();
      const { getByTestId } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          onActivityComplete={onComplete}
        />
      );

      const completeButton = getByTestId('complete-activity');
      fireEvent.press(completeButton);

      expect(onComplete).toHaveBeenCalledWith({ score: 90, points: 15 });
    });

    it('should handle activity errors', async () => {
      const onError = jest.fn();
      const { getByTestId } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          onActivityError={onError}
        />
      );

      const errorButton = getByTestId('error-activity');
      fireEvent.press(errorButton);

      expect(onError).toHaveBeenCalled();
    });
  });

  describe('Profile Screen Integration', () => {
    it('should render profile screen with user info', () => {
      const { getByText } = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
        />
      );

      expect(getByText('John Doe')).toBeTruthy();
      expect(getByText('150 points • 12 activities')).toBeTruthy();
    });

    it('should render recommended activity section', () => {
      const { getByText, getByTestId } = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
        />
      );

      expect(getByText('Recommended for You')).toBeTruthy();
      expect(getByTestId('activity-love-language-quiz')).toBeTruthy();
    });

    it('should render recent activities', () => {
      const { getByText } = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
        />
      );

      expect(getByText('Recent Activities')).toBeTruthy();
      expect(getByText('Match Activity')).toBeTruthy();
      expect(getByText('Daily Questions')).toBeTruthy();
      expect(getByText('Would You Rather')).toBeTruthy();
    });

    it('should render achievements section', () => {
      const { getByText } = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
        />
      );

      expect(getByText('Achievements')).toBeTruthy();
      expect(getByText('Streak Master')).toBeTruthy();
      expect(getByText('Activity Explorer')).toBeTruthy();
    });

    it('should handle activity completion in profile context', async () => {
      const onComplete = jest.fn();
      const { getByTestId } = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
          onActivityComplete={onComplete}
        />
      );

      const completeButton = getByTestId('complete-activity');
      fireEvent.press(completeButton);

      expect(onComplete).toHaveBeenCalledWith({ score: 90, points: 15 });
    });
  });

  describe('Cross-Screen Compatibility', () => {
    it('should render same activity consistently across screens', () => {
      const homeScreen = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      const profileScreen = render(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
        />
      );

      // Both screens should render activities with same structure
      expect(homeScreen.getByTestId('activity-match-activity')).toBeTruthy();
      expect(profileScreen.getByTestId('activity-love-language-quiz')).toBeTruthy();
    });

    it('should maintain activity state across screen changes', async () => {
      const onComplete = jest.fn();

      const { rerender } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          onActivityComplete={onComplete}
        />
      );

      // Complete activity on home screen
      const completeButton = screen.getByTestId('complete-activity');
      fireEvent.press(completeButton);

      expect(onComplete).toHaveBeenCalledWith({ score: 90, points: 15 });

      // Switch to profile screen
      rerender(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
          onActivityComplete={onComplete}
        />
      );

      // Activity should still work the same way
      const profileCompleteButton = screen.getByTestId('complete-activity');
      fireEvent.press(profileCompleteButton);

      expect(onComplete).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle errors consistently across screens', async () => {
      const onError = jest.fn();

      const { rerender } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          onActivityError={onError}
        />
      );

      // Trigger error on home screen
      const errorButton = screen.getByTestId('error-activity');
      fireEvent.press(errorButton);

      expect(onError).toHaveBeenCalled();

      // Switch to profile screen
      rerender(
        <ProfileScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
          userProfile={mockUser}
          onActivityError={onError}
        />
      );

      // Error handling should work the same way
      const profileErrorButton = screen.getByTestId('error-activity');
      fireEvent.press(profileErrorButton);

      expect(onError).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance Integration', () => {
    it('should render multiple activities without performance issues', () => {
      const startTime = Date.now();

      render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      const endTime = Date.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000);
    });

    it('should handle rapid screen switches gracefully', () => {
      const { rerender } = render(
        <HomeScreenIntegration
          userId={mockUser.id}
          coupleId={mockCouple.id}
        />
      );

      // Rapidly switch between screens
      for (let i = 0; i < 5; i++) {
        rerender(
          <ProfileScreenIntegration
            userId={mockUser.id}
            coupleId={mockCouple.id}
            userProfile={mockUser}
          />
        );
        rerender(
          <HomeScreenIntegration
            userId={mockUser.id}
            coupleId={mockCouple.id}
          />
        );
      }

      // Should not crash or have memory leaks
      expect(screen.getByText('Featured Activity')).toBeTruthy();
    });
  });
});
