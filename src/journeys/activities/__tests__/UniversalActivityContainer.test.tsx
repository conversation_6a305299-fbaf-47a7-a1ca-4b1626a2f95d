/**
 * Universal Activity Container Tests
 * Comprehensive tests for the universal activity container component
 */

import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { UniversalActivityContainer } from '../containers/UniversalActivityContainer';

// Mock the activity registry
jest.mock('../registry/ActivityRegistry', () => ({
  activityRegistry: {
    getActivity: jest.fn(),
  },
}));

// Mock the MatchActivity component
jest.mock('../activities/MatchActivity/MatchActivity', () => ({
  MatchActivity: ({ activityId, onComplete, onExit, onError }: any) => (
    <div testID="match-activity">
      <div testID="activity-title">Match Activity: {activityId}</div>
      <button testID="complete-button" onPress={() => onComplete({ score: 85, points: 10 })}>
        Complete
      </button>
      <button testID="exit-button" onPress={() => onExit()}>
        Exit
      </button>
      <button testID="error-button" onPress={() => onError(new Error('Test error'))}>
        Trigger Error
      </button>
    </div>
  ),
}));

// Mock expo-router
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
  },
}));

// Mock colors
jest.mock('../../../shared/utils/colors', () => ({
  colors: {
    primary: '#007AFF',
    background: '#FFFFFF',
    textPrimary: '#000000',
    textSecondary: '#666666',
    error: '#FF3B30',
    white: '#FFFFFF',
    shadow: '#000000',
  },
}));

describe('UniversalActivityContainer', () => {
  const defaultProps = {
    activityId: 'match-activity',
    userId: 'user-123',
    coupleId: 'couple-456',
    onComplete: jest.fn(),
    onExit: jest.fn(),
    onError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Loading States', () => {
    it('should render loading state initially', () => {
      const { getByText } = render(<UniversalActivityContainer {...defaultProps} />);

      expect(getByText('Loading Activity...')).toBeTruthy();
    });

    it('should show loading spinner', () => {
      const { getByTestId } = render(<UniversalActivityContainer {...defaultProps} />);

      // Note: ActivityIndicator doesn't have a testID by default in React Native Testing Library
      // This test would need to be adjusted based on your actual implementation
    });
  });

  describe('Error Handling', () => {
    it('should render activity not found error for invalid activity', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue(undefined);

      const { getByText } = render(<UniversalActivityContainer {...defaultProps} />);

      await waitFor(() => {
        expect(getByText('Activity Not Found')).toBeTruthy();
        expect(getByText('The activity "match-activity" could not be found.')).toBeTruthy();
      });
    });

    it('should render activity not available error for unavailable activity', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: false,
      });

      const { getByText } = render(<UniversalActivityContainer {...defaultProps} />);

      await waitFor(() => {
        expect(getByText('Activity Not Available')).toBeTruthy();
        expect(getByText('This activity will be available in a future update!')).toBeTruthy();
      });
    });

    it('should handle activity errors gracefully', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
        route: '/match-activity',
      });

      const onError = jest.fn();
      const { getByTestId } = render(
        <UniversalActivityContainer {...defaultProps} onError={onError} />
      );

      await waitFor(() => {
        const errorButton = getByTestId('error-button');
        fireEvent.press(errorButton);
        expect(onError).toHaveBeenCalled();
      });
    });
  });

  describe('Activity Rendering', () => {
    it('should render match activity when available', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
        route: '/match-activity',
      });

      const { getByTestId } = render(<UniversalActivityContainer {...defaultProps} />);

      await waitFor(() => {
        expect(getByTestId('match-activity')).toBeTruthy();
        expect(getByTestId('activity-title')).toBeTruthy();
      });
    });

    it('should handle activity completion', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
        route: '/match-activity',
      });

      const onComplete = jest.fn();
      const { getByTestId } = render(
        <UniversalActivityContainer {...defaultProps} onComplete={onComplete} />
      );

      await waitFor(() => {
        const completeButton = getByTestId('complete-button');
        fireEvent.press(completeButton);
        expect(onComplete).toHaveBeenCalledWith({ score: 85, points: 10 });
      });
    });

    it('should handle activity exit', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
        route: '/match-activity',
      });

      const onExit = jest.fn();
      const { getByTestId } = render(
        <UniversalActivityContainer {...defaultProps} onExit={onExit} />
      );

      await waitFor(() => {
        const exitButton = getByTestId('exit-button');
        fireEvent.press(exitButton);
        expect(onExit).toHaveBeenCalled();
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate to activity route when no inline component available', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      const { router } = require('expo-router');

      activityRegistry.getActivity.mockReturnValue({
        id: 'some-other-activity',
        isAvailable: true,
        route: '/some-other-activity',
      });

      render(<UniversalActivityContainer {...defaultProps} activityId="some-other-activity" />);

      await waitFor(() => {
        expect(router.push).toHaveBeenCalledWith('/some-other-activity');
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue(undefined);

      const { getByLabelText } = render(<UniversalActivityContainer {...defaultProps} />);

      await waitFor(() => {
        expect(getByLabelText('Go back to previous screen')).toBeTruthy();
      });
    });
  });

  describe('Performance', () => {
    it('should memoize activity lookup', () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
      });

      const { rerender } = render(<UniversalActivityContainer {...defaultProps} />);

      // Re-render with same props
      rerender(<UniversalActivityContainer {...defaultProps} />);

      // Activity lookup should be memoized
      expect(activityRegistry.getActivity).toHaveBeenCalledTimes(1);
    });

    it('should handle rapid prop changes gracefully', async () => {
      const { activityRegistry } = require('../registry/ActivityRegistry');
      activityRegistry.getActivity.mockReturnValue({
        id: 'match-activity',
        isAvailable: true,
      });

      const { rerender } = render(<UniversalActivityContainer {...defaultProps} />);

      // Rapidly change activityId
      rerender(<UniversalActivityContainer {...defaultProps} activityId="new-activity" />);
      rerender(<UniversalActivityContainer {...defaultProps} activityId="another-activity" />);

      // Should not crash
      expect(activityRegistry.getActivity).toHaveBeenCalled();
    });
  });
});
