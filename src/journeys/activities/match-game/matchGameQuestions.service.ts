/**
 * Match Game Questions Service
 * Handles all database operations related to match game questions
 */

import { supabase } from '../../../shared/services/supabase/client';
import { logger } from '../../../shared/utils/logger';
import {
    GetQuestionsRequest,
    MatchGameError,
    MatchGameQuestion,
    MatchGameQuestionsService,
    MATCH_GAME_DIFFICULTIES
} from '../types/matchGame.types';

// Type guard to safely transform database result to MatchGameQuestion
function transformToMatchGameQuestion(data: any): MatchGameQuestion | null {
  if (!data) return null;
  
  // Ensure difficulty is a valid value or default to 'medium'
  const validDifficulty = data.difficulty && MATCH_GAME_DIFFICULTIES.includes(data.difficulty) 
    ? data.difficulty 
    : 'medium';
  
  return {
    id: data.id,
    question_id: data.question_id,
    question_text: data.question_text,
    category: data.category,
    difficulty: validDifficulty as 'easy' | 'medium' | 'hard',
    question_type: data.question_type || 'text',
    is_active: data.is_active ?? true,
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString()
  };
}

export class MatchGameQuestionsServiceImpl implements MatchGameQuestionsService {

  /**
   * Get random questions with optional filtering
   */
  async getRandomQuestions(params: GetQuestionsRequest = {}): Promise<MatchGameQuestion[]> {
    try {
      const {
        count = 10,
        category,
        difficulty,
        exclude_answered_by_user
      } = params;

      let query = supabase
        .from('match_game_questions')
        .select('*')
        .eq('is_active', true);

      if (category) {
        query = query.eq('category', category);
      }

      if (difficulty) {
        query = query.eq('difficulty', difficulty);
      }

      if (exclude_answered_by_user) {
        // Get questions that user hasn't answered yet
        const { data: answeredQuestions } = await supabase
          .from('match_game_user_answers')
          .select('question_id')
          .eq('user_id', exclude_answered_by_user);

        if (answeredQuestions && answeredQuestions.length > 0) {
          const answeredQuestionIds = answeredQuestions.map(a => a.question_id);
          query = query.not('question_id', 'in', `(${answeredQuestionIds.join(',')})`);
        }
      }

      const { data, error } = await query
        .order('random()')
        .limit(count);

      if (error) {
        logger.error('Error fetching random questions:', error);
        throw new MatchGameError({
          code: 'FETCH_QUESTIONS_ERROR',
          message: 'Failed to fetch questions',
          details: error
        });
      }

      return (data || []).map(transformToMatchGameQuestion).filter((q): q is MatchGameQuestion => q !== null);
    } catch (error) {
      logger.error('Error in getRandomQuestions:', error);
      throw error;
    }
  }

  /**
   * Get balanced questions (40% easy, 40% medium, 20% hard)
   */
  async getBalancedQuestions(params: GetQuestionsRequest = {}): Promise<MatchGameQuestion[]> {
    try {
      const { count = 10, category, exclude_answered_by_user } = params;

      // Calculate distribution: 40% easy, 40% medium, 20% hard
      const easyCount = Math.ceil(count * 0.4);
      const mediumCount = Math.ceil(count * 0.4);
      const hardCount = count - easyCount - mediumCount;

      const questions: MatchGameQuestion[] = [];

      // Fetch questions by difficulty
      if (easyCount > 0) {
        const easyQuestions = await this.getQuestionsByDifficulty('easy', easyCount);
        questions.push(...easyQuestions);
      }

      if (mediumCount > 0) {
        const mediumQuestions = await this.getQuestionsByDifficulty('medium', mediumCount);
        questions.push(...mediumQuestions);
      }

      if (hardCount > 0) {
        const hardQuestions = await this.getQuestionsByDifficulty('hard', hardCount);
        questions.push(...hardQuestions);
      }

      // Shuffle the combined questions
      return questions.sort(() => Math.random() - 0.5);
    } catch (error) {
      logger.error('Error in getBalancedQuestions:', error);
      throw error;
    }
  }

  /**
   * Get questions by category
   */
  async getQuestionsByCategory(category: string, count: number = 10): Promise<MatchGameQuestion[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_questions')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('random()')
        .limit(count);

      if (error) {
        logger.error('Error fetching questions by category:', error);
        throw new MatchGameError({
          code: 'FETCH_QUESTIONS_BY_CATEGORY_ERROR',
          message: `Failed to fetch questions for category: ${category}`,
          details: error
        });
      }

      return (data || []).map(transformToMatchGameQuestion).filter((q): q is MatchGameQuestion => q !== null);
    } catch (error) {
      logger.error('Error in getQuestionsByCategory:', error);
      throw error;
    }
  }

  /**
   * Get questions by difficulty level
   */
  async getQuestionsByDifficulty(
    difficulty: 'easy' | 'medium' | 'hard',
    count: number = 10
  ): Promise<MatchGameQuestion[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_questions')
        .select('*')
        .eq('difficulty', difficulty)
        .eq('is_active', true)
        .order('random()')
        .limit(count);

      if (error) {
        logger.error('Error fetching questions by difficulty:', error);
        throw new MatchGameError({
          code: 'FETCH_QUESTIONS_BY_DIFFICULTY_ERROR',
          message: `Failed to fetch questions for difficulty: ${difficulty}`,
          details: error
        });
      }

      return (data || []).map(transformToMatchGameQuestion).filter((q): q is MatchGameQuestion => q !== null);
    } catch (error) {
      logger.error('Error in getQuestionsByDifficulty:', error);
      throw error;
    }
  }

  /**
   * Get all available categories
   */
  async getAllCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_questions')
        .select('category')
        .eq('is_active', true)
        .order('category');

      if (error) {
        logger.error('Error fetching categories:', error);
        throw new MatchGameError({
          code: 'FETCH_CATEGORIES_ERROR',
          message: 'Failed to fetch categories',
          details: error
        });
      }

      // Get unique categories
      const categorySet = new Set(data?.map(item => item.category) || []);
      const categories = Array.from(categorySet);
      return categories;
    } catch (error) {
      logger.error('Error in getAllCategories:', error);
      throw error;
    }
  }

  /**
   * Get a specific question by ID
   */
  async getQuestionById(question_id: string): Promise<MatchGameQuestion | null> {
    try {
      const { data, error } = await supabase
        .from('match_game_questions')
        .select('*')
        .eq('question_id', question_id)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        logger.error('Error fetching question by ID:', error);
        throw new MatchGameError({
          code: 'FETCH_QUESTION_BY_ID_ERROR',
          message: `Failed to fetch question: ${question_id}`,
          details: error
        });
      }

      return transformToMatchGameQuestion(data);
    } catch (error) {
      logger.error('Error in getQuestionById:', error);
      throw error;
    }
  }

  /**
   * Get question statistics
   */
  async getQuestionStats(): Promise<{
    total_questions: number;
    questions_by_category: Record<string, number>;
    questions_by_difficulty: Record<string, number>;
  }> {
    try {
      const { data, error } = await supabase
        .from('match_game_questions')
        .select('category, difficulty')
        .eq('is_active', true);

      if (error) {
        logger.error('Error fetching question stats:', error);
        throw new MatchGameError({
          code: 'FETCH_QUESTION_STATS_ERROR',
          message: 'Failed to fetch question statistics',
          details: error
        });
      }

      const stats = {
        total_questions: data?.length || 0,
        questions_by_category: {} as Record<string, number>,
        questions_by_difficulty: {} as Record<string, number>
      };

      // Count by category and difficulty
      data?.forEach(question => {
        stats.questions_by_category[question.category] =
          (stats.questions_by_category[question.category] || 0) + 1;
        if (question.difficulty) {
          stats.questions_by_difficulty[question.difficulty] =
            (stats.questions_by_difficulty[question.difficulty] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      logger.error('Error in getQuestionStats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const matchGameQuestionsService = new MatchGameQuestionsServiceImpl();
