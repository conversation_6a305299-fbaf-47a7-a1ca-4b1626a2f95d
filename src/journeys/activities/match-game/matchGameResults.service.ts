/**
 * Match Game Results Service
 * Handles all database operations related to game results and statistics
 */

import { supabase } from '../../../shared/services/supabase/client';
import { logger } from '../../../shared/utils/logger';
import {
    MatchGameError,
    MatchGameResult,
    MatchGameResultsService,
    MatchGameStats,
    SaveResultRequest
} from '../types/matchGame.types';

export class MatchGameResultsServiceImpl implements MatchGameResultsService {

  /**
   * Save a game result for a specific question
   */
  async saveResult(result: SaveResultRequest): Promise<MatchGameResult> {
    try {
      // Calculate if guesses were correct
      const partner1_correct = result.partner1_guess?.toLowerCase().trim() ===
                              result.partner2_answer?.toLowerCase().trim();
      const partner2_correct = result.partner2_guess?.toLowerCase().trim() ===
                              result.partner1_answer?.toLowerCase().trim();

      const { data, error } = await supabase
        .from('match_game_results')
        .insert({
          session_id: result.session_id,
          question_id: result.question_id,
          partner1_user_id: result.partner1_user_id,
          partner2_user_id: result.partner2_user_id,
          partner1_answer: result.partner1_answer,
          partner2_answer: result.partner2_answer,
          partner1_guess: result.partner1_guess,
          partner2_guess: result.partner2_guess,
          partner1_correct,
          partner2_correct
        })
        .select()
        .single();

      if (error) {
        logger.error('Error saving result:', error);
        throw new MatchGameError({
          code: 'SAVE_RESULT_ERROR',
          message: 'Failed to save game result',
          details: error
        });
      }

      if (!data || data.session_id === null || data.question_id === null) {
        throw new MatchGameError({
          code: 'INVALID_RESULT_DATA',
          message: 'Invalid result data returned from database',
          details: data
        });
      }
      
      return data as MatchGameResult;
    } catch (error) {
      logger.error('Error in saveResult:', error);
      throw error;
    }
  }

  /**
   * Get all results for a specific session
   */
  async getSessionResults(session_id: string): Promise<MatchGameResult[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_results')
        .select('*')
        .eq('session_id', session_id)
        .order('answered_at', { ascending: true });

      if (error) {
        logger.error('Error fetching session results:', error);
        throw new MatchGameError({
          code: 'FETCH_SESSION_RESULTS_ERROR',
          message: `Failed to fetch results for session: ${session_id}`,
          details: error
        });
      }

      return (data || []).filter(result => 
        result.session_id !== null && 
        result.question_id !== null
      ) as MatchGameResult[];
    } catch (error) {
      logger.error('Error in getSessionResults:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive statistics for a user
   */
  async getUserStats(user_id: string): Promise<MatchGameStats> {
    try {
      // Get user's sessions and results
      const { data: sessionsData, error: sessionsError } = await supabase
        .from('match_game_sessions')
        .select(`
          id,
          total_questions,
          correct_matches,
          created_at,
          completed,
          couples!inner(
            partner1_id,
            partner2_id
          )
        `)
        .or(`couples.partner1_id.eq.${user_id},couples.partner2_id.eq.${user_id}`)
        .eq('completed', true)
        .order('created_at', { ascending: false });

      if (sessionsError) {
        logger.error('Error fetching user sessions for stats:', sessionsError);
        throw new MatchGameError({
          code: 'FETCH_USER_SESSIONS_STATS_ERROR',
          message: `Failed to fetch sessions for user stats: ${user_id}`,
          details: sessionsError
        });
      }

      const sessions = sessionsData || [];

      // Get user's individual results
      const { data: resultsData, error: resultsError } = await supabase
        .from('match_game_results')
        .select(`
          *,
          match_game_questions!inner(
            category
          )
        `)
        .or(`partner1_user_id.eq.${user_id},partner2_user_id.eq.${user_id}`);

      if (resultsError) {
        logger.error('Error fetching user results for stats:', resultsError);
        throw new MatchGameError({
          code: 'FETCH_USER_RESULTS_STATS_ERROR',
          message: `Failed to fetch results for user stats: ${user_id}`,
          details: resultsError
        });
      }

      const results = resultsData || [];

      // Calculate statistics
      const total_sessions = sessions.length;
      const total_questions_answered = sessions.reduce((sum, s) => sum + (s.total_questions || 0), 0);
      const total_correct_matches = sessions.reduce((sum, s) => sum + (s.correct_matches || 0), 0);

      // Calculate best session score
      const sessionScores = sessions.map(s =>
        s.total_questions && s.total_questions > 0 && s.correct_matches !== null ? (s.correct_matches / s.total_questions) * 100 : 0
      );
      const best_session_score = sessionScores.length > 0 ? Math.max(...sessionScores) : 0;

      // Calculate streaks
      let current_streak = 0;
      let longest_streak = 0;
      let temp_streak = 0;

      sessions.forEach(session => {
        const score = (session.total_questions && session.total_questions > 0) && (session.correct_matches !== null) 
          ? (session.correct_matches / session.total_questions) * 100 
          : 0;
        if (score >= 50) { // Consider 50%+ as a "good" session
          temp_streak++;
          longest_streak = Math.max(longest_streak, temp_streak);
        } else {
          if (current_streak === 0) current_streak = temp_streak;
          temp_streak = 0;
        }
      });

      // Find favorite category
      const categoryCount: Record<string, number> = {};
      results.forEach(result => {
        const category = result.match_game_questions?.category || 'Unknown';
        categoryCount[category] = (categoryCount[category] || 0) + 1;
      });
      const favorite_category = Object.keys(categoryCount).reduce((a, b) =>
        categoryCount[a] > categoryCount[b] ? a : b, 'Unknown'
      );

      // Calculate average session duration (mock for now - would need session timing data)
      const average_session_duration = total_sessions > 0 ? 1800 : 0; // 30 minutes default

      return {
        total_sessions,
        total_questions_answered,
        total_correct_matches,
        best_session_score,
        current_streak,
        longest_streak,
        favorite_category,
        average_session_duration
      };
    } catch (error) {
      logger.error('Error in getUserStats:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive statistics for a couple
   */
  async getCoupleStats(couple_id: string): Promise<MatchGameStats> {
    try {
      // Get couple's sessions
      const { data: sessionsData, error: sessionsError } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('couple_id', couple_id)
        .eq('completed', true)
        .order('created_at', { ascending: false });

      if (sessionsError) {
        logger.error('Error fetching couple sessions for stats:', sessionsError);
        throw new MatchGameError({
          code: 'FETCH_COUPLE_SESSIONS_STATS_ERROR',
          message: `Failed to fetch sessions for couple stats: ${couple_id}`,
          details: sessionsError
        });
      }

      const sessions = sessionsData || [];

      // Get couple's results
      const { data: resultsData, error: resultsError } = await supabase
        .from('match_game_results')
        .select(`
          *,
          match_game_questions!inner(
            category
          ),
          match_game_sessions!inner(
            couple_id
          )
        `)
        .eq('match_game_sessions.couple_id', couple_id);

      if (resultsError) {
        logger.error('Error fetching couple results for stats:', resultsError);
        throw new MatchGameError({
          code: 'FETCH_COUPLE_RESULTS_STATS_ERROR',
          message: `Failed to fetch results for couple stats: ${couple_id}`,
          details: resultsError
        });
      }

      const results = resultsData || [];

      // Calculate statistics (similar to getUserStats but for couple)
      const total_sessions = sessions.length;
      const total_questions_answered = sessions.reduce((sum, s) => sum + (s.total_questions || 0), 0);
      const total_correct_matches = sessions.reduce((sum, s) => sum + (s.correct_matches || 0), 0);

      const sessionScores = sessions.map(s =>
        (s.total_questions && s.total_questions > 0) && (s.correct_matches !== null) 
          ? (s.correct_matches / s.total_questions) * 100 
          : 0
      );
      const best_session_score = sessionScores.length > 0 ? Math.max(...sessionScores) : 0;

      // Calculate streaks
      let current_streak = 0;
      let longest_streak = 0;
      let temp_streak = 0;

      sessions.forEach(session => {
        const score = (session.total_questions && session.total_questions > 0) && (session.correct_matches !== null) 
          ? (session.correct_matches / session.total_questions) * 100 
          : 0;
        if (score >= 50) {
          temp_streak++;
          longest_streak = Math.max(longest_streak, temp_streak);
        } else {
          if (current_streak === 0) current_streak = temp_streak;
          temp_streak = 0;
        }
      });

      // Find favorite category
      const categoryCount: Record<string, number> = {};
      results.forEach(result => {
        const category = result.match_game_questions?.category || 'Unknown';
        categoryCount[category] = (categoryCount[category] || 0) + 1;
      });
      const favorite_category = Object.keys(categoryCount).reduce((a, b) =>
        categoryCount[a] > categoryCount[b] ? a : b, 'Unknown'
      );

      const average_session_duration = total_sessions > 0 ? 1800 : 0;

      return {
        total_sessions,
        total_questions_answered,
        total_correct_matches,
        best_session_score,
        current_streak,
        longest_streak,
        favorite_category,
        average_session_duration
      };
    } catch (error) {
      logger.error('Error in getCoupleStats:', error);
      throw error;
    }
  }

  /**
   * Get category-based statistics for a user
   */
  async getCategoryStats(user_id: string): Promise<Record<string, { total: number; correct: number; percentage: number }>> {
    try {
      const { data, error } = await supabase
        .from('match_game_results')
        .select(`
          partner1_correct,
          partner2_correct,
          partner1_user_id,
          partner2_user_id,
          match_game_questions!inner(
            category
          )
        `)
        .or(`partner1_user_id.eq.${user_id},partner2_user_id.eq.${user_id}`);

      if (error) {
        logger.error('Error fetching category stats:', error);
        throw new MatchGameError({
          code: 'FETCH_CATEGORY_STATS_ERROR',
          message: `Failed to fetch category stats for user: ${user_id}`,
          details: error
        });
      }

      const results = data || [];
      const categoryStats: Record<string, { total: number; correct: number; percentage: number }> = {};

      results.forEach(result => {
        const category = result.match_game_questions?.category || 'Unknown';

        if (!categoryStats[category]) {
          categoryStats[category] = { total: 0, correct: 0, percentage: 0 };
        }

        categoryStats[category].total++;

        // Check if this user got it correct
        const isUserPartner1 = result.partner1_user_id === user_id;
        const userCorrect = isUserPartner1 ? result.partner1_correct : result.partner2_correct;

        if (userCorrect) {
          categoryStats[category].correct++;
        }
      });

      // Calculate percentages
      Object.keys(categoryStats).forEach(category => {
        const stats = categoryStats[category];
        stats.percentage = stats.total > 0 ? (stats.correct / stats.total) * 100 : 0;
      });

      return categoryStats;
    } catch (error) {
      logger.error('Error in getCategoryStats:', error);
      throw error;
    }
  }

  /**
   * Get difficulty-based statistics for a user
   */
  async getDifficultyStats(user_id: string): Promise<Record<string, { total: number; correct: number; percentage: number }>> {
    try {
      const { data, error } = await supabase
        .from('match_game_results')
        .select(`
          partner1_correct,
          partner2_correct,
          partner1_user_id,
          partner2_user_id,
          match_game_questions!inner(
            difficulty
          )
        `)
        .or(`partner1_user_id.eq.${user_id},partner2_user_id.eq.${user_id}`);

      if (error) {
        logger.error('Error fetching difficulty stats:', error);
        throw new MatchGameError({
          code: 'FETCH_DIFFICULTY_STATS_ERROR',
          message: `Failed to fetch difficulty stats for user: ${user_id}`,
          details: error
        });
      }

      const results = data || [];
      const difficultyStats: Record<string, { total: number; correct: number; percentage: number }> = {};

      results.forEach(result => {
        const difficulty = result.match_game_questions?.difficulty || 'Unknown';

        if (!difficultyStats[difficulty]) {
          difficultyStats[difficulty] = { total: 0, correct: 0, percentage: 0 };
        }

        difficultyStats[difficulty].total++;

        const isUserPartner1 = result.partner1_user_id === user_id;
        const userCorrect = isUserPartner1 ? result.partner1_correct : result.partner2_correct;

        if (userCorrect) {
          difficultyStats[difficulty].correct++;
        }
      });

      // Calculate percentages
      Object.keys(difficultyStats).forEach(difficulty => {
        const stats = difficultyStats[difficulty];
        stats.percentage = stats.total > 0 ? (stats.correct / stats.total) * 100 : 0;
      });

      return difficultyStats;
    } catch (error) {
      logger.error('Error in getDifficultyStats:', error);
      throw error;
    }
  }

  /**
   * Get performance trends over time
   */
  async getPerformanceTrends(user_id: string, days: number = 30): Promise<{
    date: string;
    score: number;
    questions_answered: number;
  }[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('match_game_sessions')
        .select(`
          created_at,
          total_questions,
          correct_matches,
          couples!inner(
            partner1_id,
            partner2_id
          )
        `)
        .or(`couples.partner1_id.eq.${user_id},couples.partner2_id.eq.${user_id}`)
        .eq('completed', true)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true });

      if (error) {
        logger.error('Error fetching performance trends:', error);
        throw new MatchGameError({
          code: 'FETCH_PERFORMANCE_TRENDS_ERROR',
          message: `Failed to fetch performance trends for user: ${user_id}`,
          details: error
        });
      }

      const sessions = data || [];

      return sessions.map(session => ({
        date: session.created_at ? session.created_at.split('T')[0] : new Date().toISOString().split('T')[0], // Just the date part
        score: (session.total_questions && session.total_questions > 0) && (session.correct_matches !== null) 
          ? (session.correct_matches / session.total_questions) * 100 
          : 0,
        questions_answered: session.total_questions || 0
      }));
    } catch (error) {
      logger.error('Error in getPerformanceTrends:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const matchGameResultsService = new MatchGameResultsServiceImpl();
