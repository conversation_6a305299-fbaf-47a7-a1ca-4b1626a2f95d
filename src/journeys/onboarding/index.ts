/**
 * Onboarding Journey - Index
 *
 * Centralized exports for all onboarding-related functionality:
 * - User authentication and profile setup
 * - Couple pairing and connection
 * - Initial app experience and feature introduction
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useAuth } from './useAuth';
export { useCouplePairing } from './useCouplePairing';
export { useCoupleRealtime } from './useCoupleRealtime';
export { useFrameworkReady } from './useFrameworkReady';
export { useUserProfile } from './useUserProfile';

// Services
export { default as couplePairingService } from './couplePairingService';
export { userPreferencesService } from './userPreferencesService';

// Auth services
export * from './auth/AuthContext';
export * from './auth/AuthProvider';

// Components
export { default as AuthScreen } from './AuthScreen';

// Utils
export * from './authPrompt';
export * from './onboardingStorage';

// Types
// Note: Legacy type exports removed - use types from shared/types instead
