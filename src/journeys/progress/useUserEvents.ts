import { useEffect, useState } from 'react';
import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';
import { useAuth } from '../onboarding/useAuth';
import { logEvent as centralLogEvent } from '../services/analytics/eventLogger';
import { UserEvent } from './userEventUtils';

export interface UseUserEventsReturn {
  logEvent: (eventName: string, metadata?: Record<string, any>) => Promise<boolean>;
  hasEvent: (eventName: string) => boolean;
  getUserEvents: () => UserEvent[];
  isLoading: boolean;
  error: string | null;
}

// Common event names
export const USER_EVENTS = {
  // Onboarding flow events
  ONBOARDING_STARTED: 'onboarding_started',
  ONBOARDING_INTRO_VIEWED: 'onboarding_intro_viewed',
  ONBOARDING_PARTNER_PROFILE_SET: 'onboarding_partner_profile_set',
  ONBOARDING_PARTNER_INVITED: 'onboarding_partner_invited',
  ONBOARDING_RITUAL_CONFIGURED: 'onboarding_ritual_configured',
  ONBOARDING_JOURNAL_ICON_SELECTED: 'onboarding_journal_icon_selected',
  ONBOARDING_COMPLETED: 'onboarding_completed',

  // Our Story events
  OUR_STORY_PRELUDE_STARTED: 'our_story_prelude_started',
  OUR_STORY_PRELUDE_FIRST_SAVED: 'our_story_prelude_first_saved',
  OUR_STORY_PRELUDE_COMPLETED: 'our_story_prelude_completed',

  // Post-onboarding updates
  PROFILE_UPDATED: 'profile_updated',
  RITUAL_UPDATED: 'ritual_updated',
  JOURNAL_ICON_UPDATED: 'journal_icon_updated',

  // Authentication events
  ACCOUNT_CREATED: 'account_created',
  SIGN_IN_SUCCESS: 'sign_in_success',
  SIGN_OUT: 'sign_out',

  // General events
  FEATURE_X_CLICKED: 'feature_x_clicked',
} as const;

export function useUserEvents(): UseUserEventsReturn {
  const { user, isAuthenticated } = useAuth();
  const [events, setEvents] = useState<UserEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user events when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserEvents();
    } else {
      setEvents([]);
    }
  }, [isAuthenticated, user]);

  const loadUserEvents = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('user_events')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        logger.error('Error loading user events:', fetchError);
        setError(fetchError.message);
        return;
      }

      setEvents((data || []).filter((event): event is UserEvent => event.user_id !== null));
    } catch (err) {
      logger.error('Error loading user events:', err);
      setError('Failed to load user events');
    } finally {
      setIsLoading(false);
    }
  };

  const logEvent = async (eventName: string, metadata: Record<string, any> = {}): Promise<boolean> => {
    try {
      setError(null);
      const res = await centralLogEvent(eventName, metadata);
      if (res && 'ok' in res && res.ok) {
        // Refresh from server to keep in sync
        await loadUserEvents();
        return true;
      }
      if (res && 'queued' in res && res.queued) {
        // Will be flushed on auth; reflect locally for UX if authenticated
        if (isAuthenticated && user) await loadUserEvents();
        logger.info('Event queued for later flush', { eventName });
        return true;
      }
      return false;
    } catch (err) {
      logger.error('Error logging event:', err);
      setError('Failed to log event');
      return false;
    }
  };

  const hasEvent = (eventName: string): boolean => {
    if (isAuthenticated && user) {
      return events.some(event => event.event_name === eventName);
    }
    return false;
  };

  const getUserEvents = (): UserEvent[] => {
    return events;
  };

  return {
    logEvent,
    hasEvent,
    getUserEvents,
    isLoading,
    error,
  };
}
