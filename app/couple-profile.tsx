import React, { useEffect, useState } from 'react';
import { <PERSON>ert, Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import DateTimePicker from '@react-native-community/datetimepicker';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { ArrowLeft, Check, Heart, User } from 'lucide-react-native';
import { DSButton, DSCard, DSHeaderBar, DSInput } from '../src/components/shared';
import { useUserProfile } from '../src/hooks/useUserProfile';
import { imageStorageService } from '../src/services/imageStorageService';
import HamburgerMenu from '../src/shared/components/layout/HamburgerMenu';
import { colors } from '../src/shared/utils/colors';
import { secureStorage } from '../src/utils/secureStorage';

interface ProfilePicture {
  partner1?: string;
  partner2?: string;
}

const PROFILE_PICTURES_KEY = 'profile_pictures';

export default function CoupleProfileScreen() {
  const { profile, updatePartners, updateProfilePhotos, updateRelationshipStartDate, isLoading } = useUserProfile();
  const [partner1Name, setPartner1Name] = useState('');
  const [partner2Name, setPartner2Name] = useState('');
  const [partner1Icon, setPartner1Icon] = useState('');
  const [partner2Icon, setPartner2Icon] = useState('');
  const [profilePictures, setProfilePictures] = useState<ProfilePicture>({});
  const [isSaving, setIsSaving] = useState(false);
  const [relationshipStartDate, setRelationshipStartDate] = useState<string | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Load profile pictures on mount
  useEffect(() => {
    loadProfilePictures();
  }, []);

  // Update local state when profile is loaded
  useEffect(() => {
    if (profile && !isLoading) {
      setPartner1Name(profile.partner1.name || '');
      setPartner2Name(profile.partner2.name || '');
      setPartner1Icon(profile.partner1.icon || '');
      setPartner2Icon(profile.partner2.icon || '');
      setRelationshipStartDate(profile.relationshipStartDate || null);
    }
  }, [profile, isLoading]);

  const loadProfilePictures = async () => {
    try {
      const stored = await secureStorage.getItem<string>(PROFILE_PICTURES_KEY);
      if (stored) {
        setProfilePictures(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading profile pictures:', error);
    }
  };

  const saveProfilePictures = async (pictures: ProfilePicture) => {
    try {
      await secureStorage.setItem(PROFILE_PICTURES_KEY, JSON.stringify(pictures));
    } catch (error) {
      console.error('Error saving profile pictures:', error);
    }
  };

  const pickImage = async (partner: 'partner1' | 'partner2') => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photo library.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const localUri = result.assets[0].uri;
        // Upload to Supabase Storage and persist URL to profiles table via hook
        const { getCurrentUser } = await import('../src/services/auth');
        const currentUser = getCurrentUser();
        const uid = currentUser?.id;
        if (uid) {
          const upload = await imageStorageService.uploadImage(localUri, { folder: 'profile-pictures' });
          if (upload.success && upload.image?.url) {
            // Update in Supabase via profile hook and keep local fallback
            const newPictures = { ...profilePictures, [partner]: upload.image.url };
            setProfilePictures(newPictures);
            await saveProfilePictures(newPictures);
            await updateProfilePhotos(partner === 'partner1' ? upload.image.url : undefined, partner === 'partner2' ? upload.image.url : undefined);
          } else {
            // Fallback: keep local URI only
            const newPictures = { ...profilePictures, [partner]: localUri };
            setProfilePictures(newPictures);
            await saveProfilePictures(newPictures);
          }
        } else {
          // No user id, store locally
          const newPictures = { ...profilePictures, [partner]: localUri };
          setProfilePictures(newPictures);
          await saveProfilePictures(newPictures);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const takePhoto = async (partner: 'partner1' | 'partner2') => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your camera.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const localUri = result.assets[0].uri;
        const { getCurrentUser } = await import('../src/services/auth');
        const currentUser = getCurrentUser();
        const uid = currentUser?.id;
        if (uid) {
          const upload = await imageStorageService.uploadImage(localUri, { folder: 'profile-pictures' });
          if (upload.success && upload.image?.url) {
            const newPictures = { ...profilePictures, [partner]: upload.image.url };
            setProfilePictures(newPictures);
            await saveProfilePictures(newPictures);
            await updateProfilePhotos(partner === 'partner1' ? upload.image.url : undefined, partner === 'partner2' ? upload.image.url : undefined);
          } else {
            const newPictures = { ...profilePictures, [partner]: localUri };
            setProfilePictures(newPictures);
            await saveProfilePictures(newPictures);
          }
        } else {
          const newPictures = { ...profilePictures, [partner]: localUri };
          setProfilePictures(newPictures);
          await saveProfilePictures(newPictures);
        }
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleIconSelect = (icon: string, partner: 'partner1' | 'partner2') => {
    if (partner === 'partner1') {
      setPartner1Icon(icon);
    } else {
      setPartner2Icon(icon);
    }
  };

  const handleSave = async () => {
    if (!partner1Name.trim() || !partner2Name.trim()) {
      Alert.alert('Missing Information', 'Please enter both partner names.');
      return;
    }

    if (!partner1Icon || !partner2Icon) {
      Alert.alert('Missing Information', 'Please select icons for both partners.');
      return;
    }

    setIsSaving(true);
    try {
      await updatePartners(
        { name: partner1Name.trim(), icon: partner1Icon },
        { name: partner2Name.trim(), icon: partner2Icon }
      );

      // Success toast
      (await import('../src/utils/toast')).showToast('Profile updated successfully');
      router.back();
    } catch (error) {
      console.error('Error saving profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Error', `Failed to update profile: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  const iconOptions = [
    { id: 'heart', symbol: '❤️', name: 'Heart' },
    { id: 'star', symbol: '⭐', name: 'Star' },
    { id: 'flower', symbol: '🌸', name: 'Flower' },
    { id: 'moon', symbol: '🌙', name: 'Moon' },
    { id: 'sun', symbol: '☀️', name: 'Sun' },
    { id: 'sparkles', symbol: '✨', name: 'Sparkles' },
    { id: 'crown', symbol: '👑', name: 'Crown' },
    { id: 'rainbow', symbol: '🌈', name: 'Rainbow' },
    { id: 'diamond', symbol: '💎', name: 'Diamond' }
  ];

  const getIconSymbol = (iconId: string): string => {
    const icon = iconOptions.find(opt => opt.id === iconId);
    return icon ? icon.symbol : '❓';
  };

  const renderIconSelector = (partner: 'partner1' | 'partner2', currentIcon: string, onSelect: (icon: string) => void) => (
    <View style={styles.iconSelector}>
      <Text style={styles.iconSelectorTitle}>Choose {partner === 'partner1' ? 'Partner 1' : 'Partner 2'} Icon:</Text>

      {currentIcon && (
        <View style={styles.currentIconDisplay}>
          <Text style={styles.currentIconSymbol}>{getIconSymbol(currentIcon)}</Text>
          <Text style={styles.currentIconText}>Current Icon</Text>
        </View>
      )}

      <View style={styles.iconGrid}>
        {iconOptions.map((icon) => (
          <TouchableOpacity
            key={icon.id}
            style={[
              styles.iconOption,
              currentIcon === icon.id && styles.iconOptionSelected
            ]}
            onPress={() => onSelect(icon.id)}
          >
            <Text style={styles.iconText}>{icon.symbol}</Text>
            {currentIcon === icon.id && (
              <View style={styles.checkmark}>
                <Check size={16} color={colors.white} />
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderProfilePicture = (partner: 'partner1' | 'partner2') => (
    <View style={styles.profilePictureSection}>
      <Text style={styles.sectionTitle}>{partner === 'partner1' ? 'Partner 1' : 'Partner 2'} Photo</Text>
      <View style={styles.pictureContainer}>
        {profilePictures[partner] ? (
          <Image source={{ uri: profilePictures[partner] }} style={styles.profilePicture} />
        ) : (
          <View style={styles.placeholderPicture}>
            <User size={48} color={colors.textTertiary} />
          </View>
        )}
        <View style={styles.pictureActions}>
          <DSButton
            title="Choose Photo"
            onPress={() => pickImage(partner)}
            variant="secondary"
            style={{ marginRight: 8 }}
          />
          <DSButton
            title="Take Photo"
            onPress={() => takePhoto(partner)}
            variant="secondary"
          />
        </View>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading profile...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <HamburgerMenu position="top-right" />

      <DSHeaderBar
        title="Couple Profile"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
        right={
          <TouchableOpacity onPress={handleSave} disabled={isSaving} style={{ opacity: isSaving ? 0.6 : 1 }}>
            <Text style={{ color: colors.white, fontWeight: '700' }}>{isSaving ? 'Saving...' : 'Save'}</Text>
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Partner 1 Section */}
        <DSCard>
          <Text style={styles.sectionTitle}>Partner 1</Text>
          <DSInput
            value={partner1Name}
            onChangeText={setPartner1Name}
            placeholder="Enter Partner 1's name"
            style={{ marginBottom: 20 }}
          />
          {renderIconSelector('partner1', partner1Icon, (icon) => handleIconSelect(icon, 'partner1'))}
          {renderProfilePicture('partner1')}
        </DSCard>

        {/* Partner 2 Section */}
        <DSCard>
          <Text style={styles.sectionTitle}>Partner 2</Text>
          <DSInput
            value={partner2Name}
            onChangeText={setPartner2Name}
            placeholder="Enter Partner 2's name"
            style={{ marginBottom: 20 }}
          />
          {renderIconSelector('partner2', partner2Icon, (icon) => handleIconSelect(icon, 'partner2'))}
          {renderProfilePicture('partner2')}

        {/* Relationship Start Date */}
        <DSCard>
          <Text style={styles.sectionTitle}>Relationship Start Date</Text>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={{ color: colors.textPrimary, fontWeight: '600' }}>
              {relationshipStartDate || profile.relationshipStartDate || 'Select a date'}
            </Text>
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={new Date((relationshipStartDate || profile.relationshipStartDate || new Date().toISOString().split('T')[0]) as string)}
              mode="date"
              display="default"
              onChange={async (event, selectedDate) => {
                setShowDatePicker(false);
                if (selectedDate) {
                  const iso = selectedDate.toISOString().split('T')[0];
                  setRelationshipStartDate(iso);
                  try {
                    await updateRelationshipStartDate(iso);
                    (await import('../src/shared/utils/toast')).showToast('Relationship start date saved');
                  } catch (e) {
                    Alert.alert('Error', 'Failed to save start date');
                  }
                }
              }}
            />
          )}
        </DSCard>

        </DSCard>

        {/* Encouragement */}
        <DSCard style={{ backgroundColor: colors.accentPink, alignItems: 'center' }}>
          <Heart size={32} color={colors.darkerPink} />
          <Text style={styles.encouragementTitle}>Add Photos!</Text>
          <Text style={styles.encouragementText}>
            Adding photos of yourselves makes the app more personal and helps you feel more connected to your relationship journey.
          </Text>
        </DSCard>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },

  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.white,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: colors.white,
    fontWeight: '600',
    marginLeft: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  nameInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: colors.white,
    marginBottom: 20,
  },
  iconSelector: {
    marginBottom: 20,
  },
  iconSelectorTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 12,
  },
  currentIconDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentIconSymbol: {
    fontSize: 24,
    marginRight: 8,
  },
  currentIconText: {
    fontSize: 14,
    color: '#6B7280',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  iconOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
    transform: [{ scale: 1.1 }],
  },
  iconText: {
    fontSize: 24,
  },
  checkmark: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profilePictureSection: {
    marginTop: 10,
  },
  pictureContainer: {
    alignItems: 'center',
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  placeholderPicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  pictureActions: {
    flexDirection: 'row',
    gap: 12,
  },
  pictureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  pictureButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  encouragementCard: {
    backgroundColor: '#FDF2F8',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  encouragementTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#831843',
    marginTop: 12,
    marginBottom: 8,
  },
  encouragementText: {
    fontSize: 14,
    color: '#831843',
    textAlign: 'center',
    lineHeight: 20,
  },
  dateButton: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    borderColor: colors.borderLight,
    borderWidth: 1,
    marginTop: 10,
  },
});
