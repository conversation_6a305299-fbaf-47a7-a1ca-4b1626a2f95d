import { useRouter } from 'expo-router';
import { ArrowLef<PERSON>, Heart } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { colors } from '../../src/shared/utils/colors';
// import { Header } from '../src/components/shared';

// Simple header component for testing
const Header: React.FC<{ title: string; left?: React.ReactNode }> = ({ title, left }) => (
  <View style={{
    backgroundColor: colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
  }}>
    {left}
    <Text style={{
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: 'bold',
      marginLeft: left ? 12 : 0,
    }}>{title}</Text>
  </View>
);

// Temporary interfaces until services are fully integrated
interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  source: 'global' | 'user';
  composite_id: string;
  category?: string;
  costLevel?: string;
  difficulty?: string;
  is_favorited?: boolean;
}

// Mock hook for testing - replace with real hook once services are ready
const useDateNightFavorites = () => {
  const [ideas, setIdeas] = useState<DateNightIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [favoriteLoadingStates, setFavoriteLoadingStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Mock data for testing
    const mockIdeas: DateNightIdea[] = [
      {
        id: '1',
        composite_id: 'global:1',
        title: 'Romantic Dinner at Home',
        description: 'Cook a special meal together and dine by candlelight',
        source: 'global',
        category: 'romantic',
        costLevel: 'medium',
        difficulty: 'easy',
        is_favorited: false,
      },
      {
        id: '2',
        composite_id: 'global:2',
        title: 'Stargazing Adventure',
        description: 'Find a quiet spot and watch the stars together',
        source: 'global',
        category: 'outdoor',
        costLevel: 'free',
        difficulty: 'easy',
        is_favorited: true,
      },
      {
        id: '1',
        composite_id: 'user:1',
        title: 'Our Special Coffee Shop',
        description: 'Visit the coffee shop where we had our first date',
        source: 'user',
        category: 'nostalgic',
        costLevel: 'low',
        difficulty: 'easy',
        is_favorited: false,
      },
    ];
    setIdeas(mockIdeas);
  }, []);

  const toggleFavorite = async (compositeId: string, metadata?: any) => {
    setFavoriteLoadingStates(prev => ({ ...prev, [compositeId]: true }));

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    setIdeas(prev => prev.map(idea =>
      idea.composite_id === compositeId
        ? { ...idea, is_favorited: !idea.is_favorited }
        : idea
    ));

    setFavoriteLoadingStates(prev => ({ ...prev, [compositeId]: false }));
  };

  const isFavorited = (compositeId: string) => {
    return ideas.find(idea => idea.composite_id === compositeId)?.is_favorited || false;
  };

  const favoriteIdeas = ideas.filter(idea => idea.is_favorited);

  return {
    ideas,
    isLoading,
    error,
    toggleFavorite,
    isFavorited,
    favoriteLoadingStates,
    loadIdeas: async () => {},
    refresh: async () => {},
    totalIdeas: ideas.length,
    favoriteIdeas,
    favoriteCount: favoriteIdeas.length,
  };
};

export default function TestDateNightFavoritesScreen() {
  const router = useRouter();
  const {
    ideas,
    isLoading,
    error,
    toggleFavorite,
    isFavorited,
    favoriteLoadingStates,
    loadIdeas,
    refresh,
    totalIdeas,
    favoriteIdeas,
    favoriteCount,
  } = useDateNightFavorites();

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleToggleFavorite = async (idea: DateNightIdea) => {
    try {
      await toggleFavorite(idea.composite_id, {
        user_action: 'manual_toggle',
        timestamp: Date.now(),
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorite status. Please try again.');
    }
  };

  const renderIdeaCard = (idea: DateNightIdea) => {
    const isItemFavorited = isFavorited(idea.composite_id);
    const isItemLoading = favoriteLoadingStates[idea.composite_id];

    return (
      <View key={idea.composite_id} style={styles.ideaCard}>
        <View style={styles.ideaHeader}>
          <View style={styles.ideaInfo}>
            <Text style={styles.ideaTitle}>{idea.title}</Text>
            <Text style={styles.ideaDescription} numberOfLines={2}>
              {idea.description}
            </Text>
            <View style={styles.ideaMeta}>
              <Text style={styles.metaText}>
                {idea.source === 'global' ? '🌍 Global' : '👤 User'} • {idea.category}
              </Text>
              {idea.costLevel && (
                <Text style={styles.metaText}>💰 {idea.costLevel}</Text>
              )}
              {idea.difficulty && (
                <Text style={styles.metaText}>⚡ {idea.difficulty}</Text>
              )}
            </View>
            <Text style={styles.compositeId}>ID: {idea.composite_id}</Text>
          </View>

          <TouchableOpacity
            style={[
              styles.favoriteButton,
              isItemFavorited && styles.favoriteButtonActive,
              isItemLoading && styles.favoriteButtonLoading,
            ]}
            onPress={() => handleToggleFavorite(idea)}
            disabled={isItemLoading}
          >
            <Heart
              size={24}
              color={isItemFavorited ? colors.white : colors.charcoalGray}
              fill={isItemFavorited ? colors.white : 'transparent'}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{totalIdeas}</Text>
        <Text style={styles.statLabel}>Total Ideas</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{favoriteCount}</Text>
        <Text style={styles.statLabel}>Favorited</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>
          {totalIdeas > 0 ? Math.round((favoriteCount / totalIdeas) * 100) : 0}%
        </Text>
        <Text style={styles.statLabel}>Favorite Rate</Text>
      </View>
    </View>
  );

  const renderDebugInfo = () => (
    <View style={styles.debugContainer}>
      <Text style={styles.debugTitle}>Debug Information</Text>
      <Text style={styles.debugText}>Total ideas loaded: {ideas.length}</Text>
      <Text style={styles.debugText}>Global ideas: {ideas.filter(i => i.source === 'global').length}</Text>
      <Text style={styles.debugText}>User ideas: {ideas.filter(i => i.source === 'user').length}</Text>
      <Text style={styles.debugText}>Favorited ideas: {favoriteCount}</Text>
      <Text style={styles.debugText}>Loading: {isLoading ? 'Yes' : 'No'}</Text>
      {error && <Text style={styles.errorText}>Error: {error}</Text>}

      <Text style={styles.debugSubtitle}>Sample Composite IDs:</Text>
      {ideas.slice(0, 3).map(idea => (
        <Text key={idea.composite_id} style={styles.debugText}>
          {idea.composite_id} ({idea.source})
        </Text>
      ))}
    </View>
  );

  const categories = [...new Set(ideas.map(idea => idea.category).filter(Boolean))];

  return (
    <View style={styles.container}>
      <Header
        title="Test Date Night Favorites"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content}>
        {/* Stats */}
        {renderStats()}

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={loadIdeas}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Reload Ideas</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={refresh}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Refresh All</Text>
          </TouchableOpacity>
        </View>

        {/* Category Filter */}
        {categories.length > 0 && (
          <View style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>Filter by Category:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <TouchableOpacity
                style={[
                  styles.categoryButton,
                  !selectedCategory && styles.categoryButtonActive,
                ]}
                onPress={() => setSelectedCategory(null)}
              >
                <Text style={[
                  styles.categoryButtonText,
                  !selectedCategory && styles.categoryButtonTextActive,
                ]}>All</Text>
              </TouchableOpacity>

              {categories.map(category => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category && styles.categoryButtonActive,
                  ]}
                  onPress={() => setSelectedCategory(category || null)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === category && styles.categoryButtonTextActive,
                  ]}>{category}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Debug Info */}
        {renderDebugInfo()}

        {/* Ideas List */}
        <View style={styles.ideasContainer}>
          <Text style={styles.sectionTitle}>
            Date Night Ideas {selectedCategory && `(${selectedCategory})`}
          </Text>

          {isLoading ? (
            <Text style={styles.loadingText}>Loading ideas...</Text>
          ) : ideas.length === 0 ? (
            <Text style={styles.emptyText}>No date night ideas found.</Text>
          ) : (
            ideas
              .filter(idea => !selectedCategory || idea.category === selectedCategory)
              .map(renderIdeaCard)
          )}
        </View>

        {/* Favorite Ideas Section */}
        {favoriteIdeas.length > 0 && (
          <View style={styles.favoritesSection}>
            <Text style={styles.sectionTitle}>Your Favorite Ideas ({favoriteCount})</Text>
            {favoriteIdeas.map(renderIdeaCard)}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row' as const,
    backgroundColor: colors.white,
    margin: 20,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.charcoalGray,
    textAlign: 'center' as const,
  },
  actionContainer: {
    flexDirection: 'row' as const,
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center' as const,
  },
  actionButtonText: {
    color: colors.white,
    fontWeight: '600' as const,
  },
  categoryContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.charcoalGray,
    marginBottom: 12,
  },
  categoryButton: {
    backgroundColor: colors.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    color: colors.charcoalGray,
    fontSize: 14,
  },
  categoryButtonTextActive: {
    color: colors.white,
  },
  debugContainer: {
    backgroundColor: colors.white,
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    color: colors.charcoalGray,
    marginBottom: 8,
  },
  debugSubtitle: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: colors.charcoalGray,
    marginTop: 8,
    marginBottom: 4,
  },
  debugText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: colors.charcoalGray,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  ideasContainer: {
    marginBottom: 20,
  },
  favoritesSection: {
    marginBottom: 20,
    backgroundColor: '#FFF5F5',
    paddingVertical: 20,
  },
  loadingText: {
    textAlign: 'center' as const,
    color: colors.textSecondary,
    fontSize: 16,
    marginVertical: 40,
  },
  emptyText: {
    textAlign: 'center' as const,
    color: colors.textSecondary,
    fontSize: 16,
    marginVertical: 40,
  },
  ideaCard: {
    backgroundColor: colors.white,
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ideaHeader: {
    flexDirection: 'row' as const,
    alignItems: 'flex-start' as const,
  },
  ideaInfo: {
    flex: 1,
    marginRight: 12,
  },
  ideaTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.charcoalGray,
    marginBottom: 4,
  },
  ideaDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  ideaMeta: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
    marginBottom: 4,
  },
  metaText: {
    fontSize: 12,
    color: colors.textSecondary,
    backgroundColor: colors.background,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  compositeId: {
    fontSize: 10,
    color: colors.textSecondary,
    fontFamily: 'monospace',
    marginTop: 4,
  },
  favoriteButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  favoriteButtonActive: {
    backgroundColor: colors.error,
    borderColor: colors.error,
  },
  favoriteButtonLoading: {
    opacity: 0.6,
  },
};
