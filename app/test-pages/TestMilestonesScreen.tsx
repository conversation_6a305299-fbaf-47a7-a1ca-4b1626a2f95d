import { useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { Header } from '../../src/components/shared';
import { CoupleMilestone, MilestoneTemplate } from '../../src/journeys/memories/milestoneService';
import { useMilestoneIntegration } from '../../src/journeys/memories/useMilestoneIntegration';
import { MilestoneForm } from '../../src/shared/components/features/MilestoneForm';
import { MilestoneList } from '../../src/shared/components/features/MilestoneList';
import { colors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

export default function TestMilestonesScreen() {
  const router = useRouter();
  const {
    templates,
    milestones,
    progress,
    isLoading,
    error,
    loadTemplates,
    loadMilestones,
    initializeMilestonesWithIntegration,
    refresh,
    totalMilestones,
    completedMilestones,
    overallProgress,
    isMilestoneFavorited,
    toggleMilestoneFavorite,
  } = useMilestoneIntegration();

  const [selectedMilestone, setSelectedMilestone] = useState<{
    milestone: CoupleMilestone;
    template: MilestoneTemplate;
  } | null>(null);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    // Load data on mount
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([loadTemplates(), loadMilestones()]);
    } catch (err) {
      logger.error('Error loading milestone data:', err);
    }
  };

  const handleInitializeMilestones = async () => {
    try {
      await initializeMilestonesWithIntegration();
      Alert.alert('Success', 'Milestones initialized with full system integration!');
    } catch (err) {
      Alert.alert('Error', 'Failed to initialize milestones. Please try again.');
    }
  };

  const handleMilestonePress = (milestone: CoupleMilestone, template: MilestoneTemplate) => {
    setSelectedMilestone({ milestone, template });
    setShowForm(true);
  };

  const handleFormSave = async (milestoneId: string, data: Record<string, any>) => {
    // Refresh data after save
    await refresh();
    setShowForm(false);
    setSelectedMilestone(null);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setSelectedMilestone(null);
  };

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{totalMilestones}</Text>
        <Text style={styles.statLabel}>Total Milestones</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{completedMilestones}</Text>
        <Text style={styles.statLabel}>Completed</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{overallProgress}%</Text>
        <Text style={styles.statLabel}>Progress</Text>
      </View>
    </View>
  );

  const renderDebugInfo = () => (
    <View style={styles.debugContainer}>
      <Text style={styles.debugTitle}>Debug Information</Text>
      <Text style={styles.debugText}>Templates loaded: {templates.length}</Text>
      <Text style={styles.debugText}>Milestones loaded: {milestones.length}</Text>
      <Text style={styles.debugText}>Progress entries: {progress.length}</Text>
      <Text style={styles.debugText}>Loading: {isLoading ? 'Yes' : 'No'}</Text>
      {error && <Text style={styles.errorText}>Error: {error}</Text>}
    </View>
  );

  if (showForm && selectedMilestone) {
    return (
      <View style={styles.container}>
        <Header
          title={selectedMilestone.template.title}
          left={
            <TouchableOpacity onPress={handleFormCancel}>
              <ArrowLeft size={24} color={colors.white} />
            </TouchableOpacity>
          }
        />
        <MilestoneForm
          template={selectedMilestone.template}
          milestone={selectedMilestone.milestone}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Test Milestones"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content}>
        {/* Stats */}
        {renderStats()}

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleInitializeMilestones}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Initialize Milestones</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={refresh}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Refresh Data</Text>
          </TouchableOpacity>
        </View>

        {/* Debug Info */}
        {renderDebugInfo()}

        {/* Milestone List */}
        {milestones.length > 0 ? (
          <MilestoneList
            milestones={milestones}
            templates={templates}
            onMilestonePress={handleMilestonePress}
            groupByCategory={true}
            showProgress={true}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              No milestones found. Try initializing milestones first.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row' as const,
    backgroundColor: colors.white,
    margin: 20,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center' as const,
  },
  actionContainer: {
    flexDirection: 'row' as const,
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center' as const,
  },
  actionButtonText: {
    color: colors.white,
    fontWeight: '600' as const,
  },
  debugContainer: {
    backgroundColor: colors.white,
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    color: colors.text,
    marginBottom: 8,
  },
  debugText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginBottom: 4,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center' as const,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center' as const,
    lineHeight: 24,
  },
};
