import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, BookOpen, Calendar, CheckCircle, MessageCircle, Sun } from 'lucide-react-native';
import { useWeekEightData } from '../src/journeys/daily/useWeekEightData';
import { colors } from '../src/shared/utils/colors';

export default function WeekEightScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentPhase, setCurrentPhase] = useState<'writing' | 'guessing' | 'finalizing'>('writing');

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);

  const {
    data,
    updatePerfectSaturdayGame,
    updateBlendedPerfectDay,
    updateChatPrompt,
    updateConflictMapping,
    updateCompletedSections,
  } = useWeekEightData();

  const steps = [
    { title: 'Perfect Saturday Game', icon: <Sun size={24} color={colors.white} /> },
    { title: 'Blended Perfect Day', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Conflict Mapping', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Eight! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleAdjectiveInput = (playerIndex: number, adjectiveIndex: number, value: string) => {
    if (playerIndex === 0) {
      const newAdjectives = [...data.perfectSaturdayGame.playerOneAdjectives];
      newAdjectives[adjectiveIndex] = value;
      updatePerfectSaturdayGame({ playerOneAdjectives: newAdjectives });
    } else {
      const newAdjectives = [...data.perfectSaturdayGame.playerTwoAdjectives];
      newAdjectives[adjectiveIndex] = value;
      updatePerfectSaturdayGame({ playerTwoAdjectives: newAdjectives });
    }
  };

  const handleGuessInput = (playerIndex: number, adjectiveIndex: number, value: string) => {
    if (playerIndex === 0) {
      const newGuesses = [...data.perfectSaturdayGame.playerOneGuess];
      newGuesses[adjectiveIndex] = value;
      updatePerfectSaturdayGame({ playerOneGuess: newGuesses });
    } else {
      const newGuesses = [...data.perfectSaturdayGame.playerTwoGuess];
      newGuesses[adjectiveIndex] = value;
      updatePerfectSaturdayGame({ playerTwoGuess: newGuesses });
    }
  };

  const handleFinalAdjectiveInput = (index: number, value: string) => {
    const newFinalAdjectives = [...data.perfectSaturdayGame.finalAdjectives];
    newFinalAdjectives[index] = value;
    updatePerfectSaturdayGame({ finalAdjectives: newFinalAdjectives });
  };

  const renderPerfectSaturdayGame = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Perfect Saturday Game</Text>
        <Text style={styles.sectionSubtitle}>Each writes 5 adjectives, then guess your partner's list</Text>
      </View>

      <View style={styles.phaseSelector}>
        <TouchableOpacity
          style={[
            styles.phaseButton,
            currentPhase === 'writing' && styles.phaseButtonActive
          ]}
          onPress={() => setCurrentPhase('writing')}
        >
          <Text style={[
            styles.phaseButtonText,
            currentPhase === 'writing' && styles.phaseButtonTextActive
          ]}>Writing</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.phaseButton,
            currentPhase === 'guessing' && styles.phaseButtonActive
          ]}
          onPress={() => setCurrentPhase('guessing')}
        >
          <Text style={[
            styles.phaseButtonText,
            currentPhase === 'guessing' && styles.phaseButtonTextActive
          ]}>Guessing</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.phaseButton,
            currentPhase === 'finalizing' && styles.phaseButtonActive
          ]}
          onPress={() => setCurrentPhase('finalizing')}
        >
          <Text style={[
            styles.phaseButtonText,
            currentPhase === 'finalizing' && styles.phaseButtonTextActive
          ]}>Finalizing</Text>
        </TouchableOpacity>
      </View>

      {currentPhase === 'writing' && (
        <View style={styles.gameCard}>
          <Text style={styles.gameTitle}>Write Your 5 Adjectives</Text>
          <Text style={styles.gameSubtitle}>Describe your perfect Saturday</Text>

          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player One</Text>
            {data.perfectSaturdayGame.playerOneAdjectives.map((adj: any, index: number) => (
              <View key={index} style={styles.adjectiveInput}>
                <Text style={styles.adjectiveLabel}>{index + 1}.</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="e.g., Relaxing, Adventurous, Cozy..."
                  value={adj}
                  onChangeText={(text: any) => handleAdjectiveInput(0, index, text)}
                />
              </View>
            ))}
          </View>

          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player Two</Text>
            {data.perfectSaturdayGame.playerTwoAdjectives.map((adj: any, index: number) => (
              <View key={index} style={styles.adjectiveInput}>
                <Text style={styles.adjectiveLabel}>{index + 1}.</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="e.g., Fun, Peaceful, Exciting..."
                  value={adj}
                  onChangeText={(text: any) => handleAdjectiveInput(1, index, text)}
                />
              </View>
            ))}
          </View>
        </View>
      )}

      {currentPhase === 'guessing' && (
        <View style={styles.gameCard}>
          <Text style={styles.gameTitle}>Guess Your Partner's List</Text>
          <Text style={styles.gameSubtitle}>Try to match their adjectives</Text>

          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player One's Guesses</Text>
            {data.perfectSaturdayGame.playerOneGuess.map((guess: any, index: number) => (
              <View key={index} style={styles.adjectiveInput}>
                <Text style={styles.adjectiveLabel}>{index + 1}.</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Guess Player Two's adjective..."
                  value={guess}
                  onChangeText={(text: any) => handleGuessInput(0, index, text)}
                />
              </View>
            ))}
          </View>

          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player Two's Guesses</Text>
            {data.perfectSaturdayGame.playerTwoGuess.map((guess: any, index: number) => (
              <View key={index} style={styles.adjectiveInput}>
                <Text style={styles.adjectiveLabel}>{index + 1}.</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Guess Player One's adjective..."
                  value={guess}
                  onChangeText={(text: any) => handleGuessInput(1, index, text)}
                />
              </View>
            ))}
          </View>
        </View>
      )}

      {currentPhase === 'finalizing' && (
        <View style={styles.gameCard}>
          <Text style={styles.gameTitle}>Finalize Your Combined List</Text>
          <Text style={styles.gameSubtitle}>Swap or remove adjectives to create your perfect blend</Text>

          <View style={styles.finalSection}>
            <Text style={styles.finalTitle}>Your Final 5 Adjectives</Text>
            {data.perfectSaturdayGame.finalAdjectives.map((adj: any, index: number) => (
              <View key={index} style={styles.adjectiveInput}>
                <Text style={styles.adjectiveLabel}>{index + 1}.</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Final adjective..."
                  value={adj}
                  onChangeText={(text: any) => handleFinalAdjectiveInput(index, text)}
                />
              </View>
            ))}
          </View>
        </View>
      )}
    </View>
  );

  const renderBlendedPerfectDay = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Blended Perfect Day</Text>
        <Text style={styles.sectionSubtitle}>Each partner chooses 1 adjective → blended date</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Blended Date</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Sunday morning"
            value={data.blendedPerfectDay.when}
            onChangeText={(text: any) => updateBlendedPerfectDay({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Local park, Coffee shop"
            value={data.blendedPerfectDay.where}
            onChangeText={(text: any) => updateBlendedPerfectDay({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Player One's Adjective:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Relaxing, Adventurous, Cozy"
            value={data.blendedPerfectDay.playerOneAdjective}
            onChangeText={(text: any) => updateBlendedPerfectDay({ playerOneAdjective: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Player Two's Adjective:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Fun, Peaceful, Exciting"
            value={data.blendedPerfectDay.playerTwoAdjective}
            onChangeText={(text: any) => updateBlendedPerfectDay({ playerTwoAdjective: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Blended Plan:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="Describe how you'll combine both adjectives in your date..."
            value={data.blendedPerfectDay.blendedPlan}
            onChangeText={(text: any) => updateBlendedPerfectDay({ blendedPlan: text })}
            multiline
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt: any, index: number) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>

          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>

            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderConflictMapping = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Conflict Mapping</Text>
        <Text style={styles.sectionSubtitle}>Identify deeper needs: Power / Trust / Value</Text>
      </View>

      <View style={styles.toolkitInstructions}>
        <Text style={styles.instructionText}>
          Interactive form with 3 steps: The Moment, The Deeper Need, Appreciation
        </Text>
        <Text style={styles.instructionText}>
          Explore what's really driving your conflicts
        </Text>
      </View>

      {data.conflictMapping.map((mapping: any, index: number) => (
        <View key={index} style={styles.conflictCard}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>The Moment:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Describe a recent conflict or tension..."
              value={mapping.theMoment}
              onChangeText={(text: any) => updateConflictMapping(index, { theMoment: text })}
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>The Deeper Need:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="What deeper need was being expressed? (Power, Trust, Value, etc.)"
              value={mapping.deeperNeed}
              onChangeText={(text: any) => updateConflictMapping(index, { deeperNeed: text })}
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Appreciation:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="What can you appreciate about your partner in this situation?"
              value={mapping.appreciation}
              onChangeText={(text: any) => updateConflictMapping(index, { appreciation: text })}
              multiline
            />
          </View>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderPerfectSaturdayGame();
      case 1:
        return renderBlendedPerfectDay();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderConflictMapping();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.cyan }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 8: The Perfect Saturday</Text>
        <Text style={styles.headerSubtitle}>Design your ideal day together</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step: any, index: number) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.cyan }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.cyan,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  phaseSelector: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  phaseButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  phaseButtonActive: {
    backgroundColor: colors.cyan,
  },
  phaseButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  phaseButtonTextActive: {
    color: colors.white,
  },
  gameCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  gameTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  gameSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  playerSection: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: colors.backgroundGray,
    borderRadius: 12,
  },
  playerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.green,
    marginBottom: 16,
    textAlign: 'center',
  },
  adjectiveInput: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  adjectiveLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginRight: 12,
    minWidth: 20,
  },
  finalSection: {
    padding: 16,
    backgroundColor: colors.backgroundOrange,
    borderRadius: 12,
  },
  finalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 16,
    textAlign: 'center',
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    flex: 1,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  toolkitInstructions: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.blueDark,
    marginBottom: 4,
    textAlign: 'center',
  },
  conflictCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
