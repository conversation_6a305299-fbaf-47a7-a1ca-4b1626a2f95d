/**
 * Dynamic Activity Route
 * 
 * Handles individual activity pages with dynamic IDs
 * Route: /activities/[id] where [id] is the activity identifier
 */

import { useLocalSearchParams, router } from 'expo-router';
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

export default function ActivityDetailScreen() {
  const { id } = useLocalSearchParams();

  const handleGoBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#393939" />
        </TouchableOpacity>
        <Text style={styles.title}>Activity Details</Text>
      </View>
      
      <View style={styles.content}>
        <Text style={styles.activityId}>Activity ID: {id}</Text>
        <Text style={styles.description}>
          This is a placeholder for the activity detail screen.
          The specific activity content will be loaded based on the ID: {id}
        </Text>
        
        <TouchableOpacity 
          style={styles.button}
          onPress={() => router.push('/(tabs)/activities')}
        >
          <Text style={styles.buttonText}>Back to Activities</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#393939',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  activityId: {
    fontSize: 18,
    fontWeight: '600',
    color: '#9CAF88',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    marginBottom: 32,
  },
  button: {
    backgroundColor: '#9CAF88',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
