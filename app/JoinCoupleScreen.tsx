/**
 * Join <PERSON>uple Screen
 *
 * Allows users to join an existing couple using a pairing code.
 * Includes attempt limiting, validation, and success handling.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { router } from 'expo-router';
import { AlertCircle, CheckCircle, Users } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { KeyboardAvoidingView, Platform, StyleSheet, Text, View } from 'react-native';
import { useAuth } from '../src/hooks/useAuth';
import { useCouplePairing } from '../src/journeys/onboarding/useCouplePairing';
import { logEvent } from '../src/journeys/progress/eventLogger';
import { colors } from '../src/shared/utils/colors';

// Import shared auth components
import {
    AuthButton,
    AuthHeader,
    AuthInput,
    AuthScreenLayout,
} from '../src/components/shared/AuthComponents';

export default function JoinCoupleScreen() {
  const [joinCode, setJoinCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    joinCouple,
    pairingStatus,
    checkPairingStatus,
    couple
  } = useCouplePairing();

  const { isAuthenticated, user } = useAuth();

  // Check pairing status on mount
  useEffect(() => {
    if (isAuthenticated && user) {
      checkPairingStatus();
    }
  }, [isAuthenticated, user]);

  // Handle URL parameters (deep linking)
  useEffect(() => {
    // This would handle deep link parameters if coming from QR code
    // For now, we'll implement basic functionality
  }, []);

  const handleJoinCouple = async () => {
    if (!joinCode.trim()) {
      setError('Please enter a pairing code');
      return;
    }

    if (!pairingStatus?.can_attempt) {
      setError('Maximum pairing attempts exceeded. Please contact support.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await joinCouple(joinCode.trim());

      if (success) {
        setSuccess(true);
        await logEvent('onboarding_partner_invited');

        // Show success message briefly, then navigate
        setTimeout(() => {
          router.replace('/our-story?mode=edit');
        }, 2000);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleCreateInstead = () => {
    router.replace('/onboarding');
  };

  // Success state
  if (success && couple) {
    return (
      <AuthScreenLayout>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <View style={styles.content}>
            <View style={styles.successContainer}>
              <CheckCircle size={64} color={colors.primary} />
              <Text style={styles.successTitle}>Welcome to the couple!</Text>
              <Text style={styles.successSubtitle}>
                You've successfully joined your partner's journal.
                Let's start your story together!
              </Text>
            </View>
          </View>
        </KeyboardAvoidingView>
      </AuthScreenLayout>
    );
  }

  // Blocked state
  if (pairingStatus?.is_blocked) {
    return (
      <AuthScreenLayout>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <AuthHeader
            title="Pairing Blocked"
            subtitle="You've exceeded the maximum number of pairing attempts"
            logo={<AlertCircle size={48} color={colors.error} />}
          />

          <View style={styles.content}>
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>
                For security reasons, pairing has been temporarily blocked after multiple failed attempts.
              </Text>
              <Text style={styles.errorSubtext}>
                Please contact support if you need assistance.
              </Text>
            </View>

            <View style={styles.buttonContainer}>
              <AuthButton
                title="Go Back"
                onPress={handleGoBack}
                variant="secondary"
              />
              <AuthButton
                title="Create New Couple"
                onPress={handleCreateInstead}
                variant="primary"
                style={styles.createButton}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </AuthScreenLayout>
    );
  }

  // Main join form
  return (
    <AuthScreenLayout>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <AuthHeader
          title="Join Your Partner"
          subtitle="Enter the pairing code your partner shared with you"
          logo={<Users size={48} color={colors.primary} />}
        />

        <View style={styles.content}>
          <View style={styles.formContainer}>
            <AuthInput
              value={joinCode}
              onChangeText={(text: any) => {
                setJoinCode(text.toUpperCase());
                setError(null);
              }}
              placeholder="Enter 8-character code"
              label="Pairing Code"
              maxLength={8}
              autoCapitalize="characters"
              autoCorrect={false}
              style={styles.codeInput}
            />

            {pairingStatus && (
              <Text style={styles.attemptsText}>
                {pairingStatus.attempts_remaining} attempts remaining
              </Text>
            )}

            {error && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={colors.error} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </View>

          <View style={styles.buttonContainer}>
            <AuthButton
              title={isLoading ? "Joining..." : "Join Couple"}
              onPress={handleJoinCouple}
              variant="primary"
              disabled={isLoading || !joinCode.trim() || !pairingStatus?.can_attempt}
              style={styles.joinButton}
            />

            <AuthButton
              title="Go Back"
              onPress={handleGoBack}
              variant="secondary"
              style={styles.backButton}
            />
          </View>

          <View style={styles.alternativeContainer}>
            <Text style={styles.alternativeText}>
              Don't have a code?
            </Text>
            <AuthButton
              title="Create New Couple"
              onPress={handleCreateInstead}
              variant="secondary"
              style={styles.createButton}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  formContainer: {
    marginBottom: 32,
  },
  codeInput: {
    fontSize: 18,
    letterSpacing: 2,
    textAlign: 'center',
    fontFamily: 'monospace',
  },
  attemptsText: {
    color: colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: `${colors.error}20`,
    borderRadius: 8,
    gap: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    textAlign: 'center',
    flex: 1,
  },
  errorSubtext: {
    color: colors.textSecondary,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  successTitle: {
    color: colors.white,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  successSubtitle: {
    color: colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    gap: 16,
    marginBottom: 32,
  },
  joinButton: {
    // Primary button styles from AuthComponents
  },
  backButton: {
    // Secondary button styles from AuthComponents
  },
  alternativeContainer: {
    alignItems: 'center',
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  alternativeText: {
    color: colors.textSecondary,
    fontSize: 14,
    marginBottom: 16,
  },
  createButton: {
    // Outline button styles from AuthComponents
  },
});
