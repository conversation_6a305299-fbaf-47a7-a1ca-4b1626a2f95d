import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, BookOpen, Calendar, CheckCircle, Heart, MessageCircle, Minus, Plus } from 'lucide-react-native';
import { useWeekSixData } from '../src/journeys/daily/useWeekSixData';
import { colors } from '../src/shared/utils/colors';

export default function WeekSixScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentMemoryIndex, setCurrentMemoryIndex] = useState(0);
  const [isPlayerOneTurn, setIsPlayerOneTurn] = useState(true);
  const [newStory, setNewStory] = useState('');

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);

  const {
    data,
    updateChildhoodMemory,
    updateMemoryLaneDate,
    updateChatPrompt,
    updateValidationPractice,
    updateCompletedSections,
  } = useWeekSixData();

  const steps = [
    { title: 'Sharing Memories', icon: <Heart size={24} color={colors.white} /> },
    { title: 'Memory Lane Date', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Validation Toolkit', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Six! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleMemoryInput = (text: string) => {
    if (isPlayerOneTurn) {
      updateChildhoodMemory(currentMemoryIndex, { memory: text });
    } else {
      updateChildhoodMemory(currentMemoryIndex, { partnerGuess: text });
    }
  };

  const handleNextMemory = () => {
    if (currentMemoryIndex < data.childhoodMemories.length - 1) {
      setCurrentMemoryIndex(currentMemoryIndex + 1);
      setIsPlayerOneTurn(!isPlayerOneTurn);
    } else {
      // All memories completed
      const newSections = [...data.completedSections];
      newSections[0] = true;
      updateCompletedSections(newSections);
    }
  };

  const addSharedStory = () => {
    if (newStory.trim()) {
      const updatedStories = [...data.memoryLaneDate.sharedStories, newStory.trim()];
      updateMemoryLaneDate({ sharedStories: updatedStories });
      setNewStory('');
    }
  };

  const removeSharedStory = (index: number) => {
    const updatedStories = data.memoryLaneDate.sharedStories.filter((_: any, i: any) => i !== index);
    updateMemoryLaneDate({ sharedStories: updatedStories });
  };

  const renderSharingMemories = () => {
    const currentMemory = data.childhoodMemories[currentMemoryIndex];

    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Sharing Memories</Text>
          <Text style={styles.sectionSubtitle}>Share your favorite childhood moments</Text>
        </View>

        <View style={styles.memoryInstructions}>
          <Text style={styles.instructionText}>
            {isPlayerOneTurn ? 'Player One writes their favorite childhood memory' : 'Player Two guesses why it\'s meaningful'}
          </Text>
          <Text style={styles.instructionText}>
            {isPlayerOneTurn ? 'Player Two will guess next' : 'Discuss and laugh together'}
          </Text>
        </View>

        <View style={styles.memoryCard}>
          <Text style={styles.memoryPrompt}>
            {isPlayerOneTurn ? 'Write your favorite childhood memory:' : 'Guess why this memory is meaningful:'}
          </Text>

          <TextInput
            style={styles.memoryInput}
            multiline
            placeholder={isPlayerOneTurn ? "Describe your favorite childhood moment..." : "Why do you think this memory is special?"}
            value={isPlayerOneTurn ? currentMemory.memory : currentMemory.partnerGuess}
            onChangeText={handleMemoryInput}
            textAlignVertical="top"
          />

          <View style={styles.memoryActions}>
            {isPlayerOneTurn ? (
              <TouchableOpacity style={styles.nextButton} onPress={() => setIsPlayerOneTurn(false)}>
                <Text style={styles.nextButtonText}>Next Player's Turn</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={styles.nextButton} onPress={handleNextMemory}>
                <Text style={styles.nextButtonText}>
                  {currentMemoryIndex < data.childhoodMemories.length - 1 ? 'Next Memory' : 'Complete Activity'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.progressIndicator}>
          <Text style={styles.progressText}>
            Memory {currentMemoryIndex + 1} of {data.childhoodMemories.length}
          </Text>
        </View>
      </View>
    );
  };

  const renderMemoryLaneDate = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Memory Lane Date</Text>
        <Text style={styles.sectionSubtitle}>Share old photos and videos together</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Memory Lane Date</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Saturday evening"
            value={data.memoryLaneDate.when}
            onChangeText={(text: any) => updateMemoryLaneDate({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., At home with photo albums"
            value={data.memoryLaneDate.where}
            onChangeText={(text: any) => updateMemoryLaneDate({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Shared Stories:</Text>
          <View style={styles.storyInputContainer}>
            <TextInput
              style={[styles.textInput, styles.storyInput]}
              placeholder="Add a story you shared..."
              value={newStory}
              onChangeText={setNewStory}
            />
            <TouchableOpacity style={styles.addButton} onPress={addSharedStory}>
              <Plus size={20} color={colors.white} />
            </TouchableOpacity>
          </View>

          {data.memoryLaneDate.sharedStories.map((story: any, index: number) => (
            <View key={index} style={styles.storyItem}>
              <Text style={styles.storyText}>{story}</Text>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeSharedStory(index)}
              >
                <Minus size={16} color={colors.error} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt: any, index: number) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>

          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>

            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderValidationToolkit = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Validation Toolkit</Text>
        <Text style={styles.sectionSubtitle}>Practice Dr. Orna Guralnik\'s validation technique</Text>
      </View>

      <View style={styles.toolkitInstructions}>
        <Text style={styles.instructionText}>
          Remember: Pause → Name → Empathize
        </Text>
        <Text style={styles.instructionText}>
          Practice turning harsh statements into validating ones
        </Text>
      </View>

      {data.validationPractice.map((practice: any, index: number) => (
        <View key={index} style={styles.validationCard}>
          <Text style={styles.originalStatement}>
            Original: "{practice.originalStatement}"
          </Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Validating phrase I could have used:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., I can see you're feeling unheard right now..."
              value={practice.validatingPhrase}
              onChangeText={(text: any) => updateValidationPractice(index, { validatingPhrase: text })}
              multiline
            />
          </View>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderSharingMemories();
      case 1:
        return renderMemoryLaneDate();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderValidationToolkit();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.error }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 6: Sharing Memories</Text>
        <Text style={styles.headerSubtitle}>Create a memory lane together</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step: any, index: number) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.error }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.error,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  memoryInstructions: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.orangeDark,
    marginBottom: 4,
    textAlign: 'center',
  },
  memoryCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  memoryPrompt: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  memoryInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    marginBottom: 16,
  },
  memoryActions: {
    alignItems: 'center',
  },
  nextButton: {
    backgroundColor: colors.error,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  nextButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  progressIndicator: {
    alignItems: 'center',
    marginTop: 16,
  },
  progressText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  storyInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storyInput: {
    flex: 1,
    marginRight: 8,
  },
  addButton: {
    backgroundColor: colors.success,
    padding: 12,
    borderRadius: 8,
  },
  storyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundGray,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  storyText: {
    flex: 1,
    fontSize: 14,
    color: colors.textPrimary,
  },
  removeButton: {
    padding: 4,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  toolkitInstructions: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  validationCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  originalStatement: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.redDark,
    marginBottom: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
