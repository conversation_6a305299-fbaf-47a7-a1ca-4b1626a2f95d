/**
 * Help & Support Route
 * 
 * Placeholder help and support screen
 * This creates the /help route that navigation calls reference
 */

import { router } from 'expo-router';
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { ArrowLeft, HelpCircle, Mail, MessageCircle } from 'lucide-react-native';

export default function HelpScreen() {
  const handleGoBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#393939" />
        </TouchableOpacity>
        <Text style={styles.title}>Help & Support</Text>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <HelpCircle size={48} color="#9CAF88" style={styles.icon} />
          <Text style={styles.sectionTitle}>How can we help you?</Text>
          <Text style={styles.sectionDescription}>
            We're here to help you make the most of your Nestled experience.
          </Text>
        </View>

        <View style={styles.helpOptions}>
          <TouchableOpacity style={styles.helpOption}>
            <MessageCircle size={24} color="#9CAF88" />
            <View style={styles.helpOptionContent}>
              <Text style={styles.helpOptionTitle}>FAQ</Text>
              <Text style={styles.helpOptionDescription}>
                Find answers to common questions
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.helpOption}>
            <Mail size={24} color="#9CAF88" />
            <View style={styles.helpOptionContent}>
              <Text style={styles.helpOptionTitle}>Contact Support</Text>
              <Text style={styles.helpOptionDescription}>
                Get in touch with our support team
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#393939',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    alignItems: 'center',
    marginBottom: 32,
  },
  icon: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#393939',
    marginBottom: 8,
    textAlign: 'center',
  },
  sectionDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  helpOptions: {
    gap: 16,
  },
  helpOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  helpOptionContent: {
    marginLeft: 16,
    flex: 1,
  },
  helpOptionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#393939',
    marginBottom: 4,
  },
  helpOptionDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
});
