/**
 * Daily Questions Settings Screen
 *
 * Settings interface for daily questions notifications and preferences.
 * Allows users to customize their notification experience.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { router } from 'expo-router';
import { ArrowLeft, Bell, Clock, MessageCircle, Settings } from 'lucide-react-native';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { DSButton, DSCard } from '../src/components/shared';
import { useDailyQuestionsNotifications } from '../src/journeys/daily/useDailyQuestionsNotifications';
import { useGlobalTheme as useTheme } from '../src/shared/components/common/ThemeProvider';
import HamburgerMenu from '../src/shared/components/layout/HamburgerMenu';
import { tokens } from '../src/utils/theme';

export default function DailyQuestionsSettingsScreen() {
  const { currentTheme } = useTheme();
  const {
    settings,
    isLoading
  } = useDailyQuestionsNotifications();

  // Mock data for now - these properties don't exist in the hook
  const error = null;
  const updateSettings = async (newSettings: any) => { console.log('Update settings:', newSettings); return true; };
  const sendPartnerResponseNotification = async () => { console.log('Send partner notification'); };
  const sendStreakMilestoneNotification = async () => { console.log('Send streak notification'); };
  const pendingNotifications: any[] = [];
  const markNotificationAsSent = (id: string) => { console.log('Mark notification sent:', id); };
  const clearOldNotifications = async () => { console.log('Clear old notifications'); };

  const [isUpdating, setIsUpdating] = useState(false);

  const handleToggleSetting = async (key: string, value: boolean) => {
    if (!settings) return;

    setIsUpdating(true);
    try {
      const success = await updateSettings({ [key]: value });
      if (!success) {
        Alert.alert('Error', 'Failed to update setting. Please try again.');
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTimeChange = async (key: 'morning_time' | 'evening_time', time: string) => {
    if (!settings) return;

    setIsUpdating(true);
    try {
      const success = await updateSettings({ [key]: time });
      if (!success) {
        Alert.alert('Error', 'Failed to update time. Please try again.');
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTestNotification = async () => {
    // For now, just show a success message
    Alert.alert('Test Sent', 'A test notification would be sent!');
  };

  const handleEnableAll = async () => {
    if (settings) {
      const success = await updateSettings({
        morning_reminder: true,
        evening_reminder: true,
        partner_response_notifications: true,
        streak_milestone_notifications: true
      });
      if (success) {
        Alert.alert('Success', 'All notifications have been enabled!');
      } else {
        Alert.alert('Error', 'Failed to enable notifications.');
      }
    }
  };

  const handleDisableAll = async () => {
    Alert.alert(
      'Disable All Notifications',
      'Are you sure you want to disable all daily questions notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disable',
          style: 'destructive',
          onPress: async () => {
            if (settings) {
              const success = await updateSettings({
                morning_reminder: false,
                evening_reminder: false,
                partner_response_notifications: false,
                streak_milestone_notifications: false
              });
              if (success) {
                Alert.alert('Success', 'All notifications have been disabled.');
              } else {
                Alert.alert('Error', 'Failed to disable notifications.');
              }
            }
          }
        }
      ]
    );
  };

  const handleClearNotifications = async () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all pending notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            await clearOldNotifications();
            Alert.alert('Success', 'All notifications have been cleared.');
          }
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            Loading settings...
          </Text>
        </View>
      </View>
    );
  }

  if (error || !settings) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.errorContainer}>
          <Settings size={48} color={currentTheme.textSecondary} />
          <Text style={[styles.errorTitle, { color: currentTheme.textPrimary }]}>
            Unable to Load Settings
          </Text>
          <Text style={[styles.errorText, { color: currentTheme.textSecondary }]}>
            {error || 'Failed to load notification settings. Please try again.'}
          </Text>
          <DSButton
            title="Go Back"
            onPress={() => router.back()}
            variant="outline"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
      <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={currentTheme.textPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: currentTheme.textPrimary }]}>
          Daily Questions Settings
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Notification Settings */}
        <DSCard style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Bell size={20} color={currentTheme.primary} />
            <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
              Notification Settings
            </Text>
          </View>

          {/* Morning Reminder */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Morning Reminder
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                Get notified when your daily question is ready
              </Text>
            </View>
            <Switch
              value={settings?.morning_reminder || false}
              onValueChange={(value: any) => handleToggleSetting('morning_reminder', value)}
              disabled={isUpdating}
            />
          </View>

          {/* Evening Reminder */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Evening Reminder
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                Reminder if you haven't answered today's question
              </Text>
            </View>
            <Switch
              value={settings?.evening_reminder || false}
              onValueChange={(value: any) => handleToggleSetting('evening_reminder', value)}
              disabled={isUpdating}
            />
          </View>

          {/* Partner Response Notifications */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Partner Response Notifications
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                Get notified when your partner answers
              </Text>
            </View>
            <Switch
              value={false}
              onValueChange={(value: any) => handleToggleSetting('partner_response_notifications', value)}
              disabled={isUpdating}
            />
          </View>

          {/* Streak Milestone Notifications */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Streak Milestone Notifications
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                Celebrate your daily question streaks
              </Text>
            </View>
            <Switch
              value={settings?.streak_milestone_notifications || false}
              onValueChange={(value: any) => handleToggleSetting('streak_milestone_notifications', value)}
              disabled={isUpdating}
            />
          </View>
        </DSCard>

        {/* Timing Settings */}
        <DSCard style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color={currentTheme.primary} />
            <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
              Timing Settings
            </Text>
          </View>

          {/* Morning Time */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Morning Reminder Time
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                When to receive your morning notification
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.timeButton, { backgroundColor: currentTheme.backgroundSecondary }]}
              onPress={() => {
                // In a real app, you'd open a time picker
                Alert.alert('Time Picker', 'Time picker would open here');
              }}
            >
              <Text style={[styles.timeText, { color: currentTheme.textPrimary }]}>
                {settings?.morning_time || '09:00'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Evening Time */}
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: currentTheme.textPrimary }]}>
                Evening Reminder Time
              </Text>
              <Text style={[styles.settingDescription, { color: currentTheme.textSecondary }]}>
                When to receive your evening reminder
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.timeButton, { backgroundColor: currentTheme.backgroundSecondary }]}
              onPress={() => {
                // In a real app, you'd open a time picker
                Alert.alert('Time Picker', 'Time picker would open here');
              }}
            >
              <Text style={[styles.timeText, { color: currentTheme.textPrimary }]}>
                {settings?.evening_time || '20:00'}
              </Text>
            </TouchableOpacity>
          </View>
        </DSCard>

        {/* Quick Actions */}
        <DSCard style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Settings size={20} color={currentTheme.primary} />
            <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
              Quick Actions
            </Text>
          </View>

          <View style={styles.actionButtons}>
            <DSButton
              title="Test Notification"
              onPress={handleTestNotification}
              variant="outline"
            />
            <DSButton
              title="Enable All"
              onPress={handleEnableAll}
              variant="outline"
            />
            <DSButton
              title="Disable All"
              onPress={handleDisableAll}
              variant="outline"
            />
          </View>
        </DSCard>

        {/* Pending Notifications */}
        {pendingNotifications.length > 0 && (
          <DSCard style={styles.settingsCard}>
            <View style={styles.sectionHeader}>
              <MessageCircle size={20} color={currentTheme.primary} />
              <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
                Pending Notifications ({pendingNotifications.length})
              </Text>
            </View>

            {pendingNotifications.map((notification: any) => (
              <View key={notification.id} style={styles.notificationRow}>
                <View style={styles.notificationInfo}>
                  <Text style={[styles.notificationTitle, { color: currentTheme.textPrimary }]}>
                    {notification.title}
                  </Text>
                  <Text style={[styles.notificationBody, { color: currentTheme.textSecondary }]}>
                    {notification.body}
                  </Text>
                </View>
                <TouchableOpacity
                  style={[styles.clearButton, { backgroundColor: currentTheme.error }]}
                  onPress={() => markNotificationAsSent(notification.id)}
                >
                  <Text style={styles.clearButtonText}>Clear</Text>
                </TouchableOpacity>
              </View>
            ))}

            <DSButton
              title="Clear All Notifications"
              onPress={handleClearNotifications}
              variant="outline"
            />
          </DSCard>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: tokens.spacing.xl,
  },
  errorTitle: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginTop: tokens.spacing.lg,
    marginBottom: tokens.spacing.sm,
  },
  errorText: {
    fontSize: tokens.typography.fontSize.md,
    textAlign: 'center',
    marginBottom: tokens.spacing.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.lg,
    paddingVertical: tokens.spacing.md,
  },
  backButton: {
    padding: tokens.spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: tokens.spacing.lg,
  },
  settingsCard: {
    marginBottom: tokens.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.lg,
  },
  sectionTitle: {
    marginLeft: tokens.spacing.sm,
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: tokens.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  settingInfo: {
    flex: 1,
    marginRight: tokens.spacing.md,
  },
  settingTitle: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.medium,
    marginBottom: tokens.spacing.xs,
  },
  settingDescription: {
    fontSize: tokens.typography.fontSize.sm,
    lineHeight: tokens.typography.lineHeight.normal,
  },
  timeButton: {
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    borderRadius: tokens.radii.md,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  timeText: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing.sm,
  },
  notificationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: tokens.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  notificationInfo: {
    flex: 1,
    marginRight: tokens.spacing.md,
  },
  notificationTitle: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    marginBottom: tokens.spacing.xs,
  },
  notificationBody: {
    fontSize: tokens.typography.fontSize.xs,
    lineHeight: tokens.typography.lineHeight.tight,
  },
  clearButton: {
    paddingHorizontal: tokens.spacing.sm,
    paddingVertical: tokens.spacing.xs,
    borderRadius: tokens.radii.sm,
  },
  clearButtonText: {
    color: 'white',
    fontSize: tokens.typography.fontSize.xs,
    fontWeight: tokens.typography.fontWeight.medium,
  },
});
