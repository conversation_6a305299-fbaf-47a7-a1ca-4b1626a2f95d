import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, BookOpen, Calendar, CheckCircle, Heart, MessageCircle } from 'lucide-react-native';
import { useWeekElevenData } from '../src/journeys/daily/useWeekElevenData';
import { colors } from '../src/shared/utils/colors';

export default function WeekElevenScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);

  const {
    data,
    updateLoveLanguageQuiz,
    updateMiniOlympics,
    updateChatPrompt,
    updateLoveLanguagePractice,
    updateCompletedSections,
  } = useWeekElevenData();

  const steps = [
    { title: 'Love Language Quiz', icon: <Heart size={24} color={colors.white} /> },
    { title: 'Mini-Olympics', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Love Language Practice', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Eleven! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleQuizAnswer = (playerIndex: number, answer: string) => {
    const newQuestions = [...data.loveLanguageQuiz.questions];
    if (playerIndex === 0) {
      newQuestions[currentQuestionIndex].playerOneAnswer = answer;
    } else {
      newQuestions[currentQuestionIndex].playerTwoAnswer = answer;
    }
    updateLoveLanguageQuiz({ questions: newQuestions });
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < data.loveLanguageQuiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // Quiz completed, calculate results
      calculateResults();
    }
  };

  const calculateResults = () => {
    // Simple scoring system - in a real app, you'd have more sophisticated logic
    const results = {
      words: Math.floor(Math.random() * 20) + 10,
      touch: Math.floor(Math.random() * 20) + 10,
      gifts: Math.floor(Math.random() * 20) + 10,
      qualityTime: Math.floor(Math.random() * 20) + 10,
      acts: Math.floor(Math.random() * 20) + 10,
    };
    updateLoveLanguageQuiz({ results });
  };

  const renderLoveLanguageQuiz = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Love Language Quiz</Text>
        <Text style={styles.sectionSubtitle}>Discover your love languages through 10 questions</Text>
      </View>

      <View style={styles.quizCard}>
        <View style={styles.progressBar}>
          <Text style={styles.progressText}>
            Question {currentQuestionIndex + 1} of {data.loveLanguageQuiz.questions.length}
          </Text>
          <View style={styles.progressBarFill}>
            <View
              style={[
                styles.progressBarProgress,
                { width: `${((currentQuestionIndex + 1) / data.loveLanguageQuiz.questions.length) * 100}%` }
              ]}
            />
          </View>
        </View>

        <Text style={styles.questionText}>
          {data.loveLanguageQuiz.questions[currentQuestionIndex].question}
        </Text>

        <View style={styles.answerSection}>
          <View style={styles.playerAnswer}>
            <Text style={styles.playerLabel}>Player One:</Text>
            <TextInput
              style={[styles.textInput, { minHeight: 80 }]}
              placeholder="Your answer..."
              value={data.loveLanguageQuiz.questions[currentQuestionIndex].playerOneAnswer}
              onChangeText={(text: any) => handleQuizAnswer(0, text)}
              multiline
            />
          </View>

          <View style={styles.playerAnswer}>
            <Text style={styles.playerLabel}>Player Two:</Text>
            <TextInput
              style={[styles.textInput, { minHeight: 80 }]}
              placeholder="Your answer..."
              value={data.loveLanguageQuiz.questions[currentQuestionIndex].playerTwoAnswer}
              onChangeText={(text: any) => handleQuizAnswer(1, text)}
              multiline
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.nextButton}
          onPress={handleNextQuestion}
          disabled={!data.loveLanguageQuiz.questions[currentQuestionIndex].playerOneAnswer ||
                   !data.loveLanguageQuiz.questions[currentQuestionIndex].playerTwoAnswer}
        >
          <Text style={styles.nextButtonText}>
            {currentQuestionIndex < data.loveLanguageQuiz.questions.length - 1 ? 'Next Question' : 'See Results'}
          </Text>
        </TouchableOpacity>

        {data.loveLanguageQuiz.results.words > 0 && (
          <View style={styles.resultsSection}>
            <Text style={styles.resultsTitle}>Your Love Language Results</Text>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Words of Affirmation:</Text>
              <Text style={styles.resultScore}>{data.loveLanguageQuiz.results.words}</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Physical Touch:</Text>
              <Text style={styles.resultScore}>{data.loveLanguageQuiz.results.touch}</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Receiving Gifts:</Text>
              <Text style={styles.resultScore}>{data.loveLanguageQuiz.results.gifts}</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Quality Time:</Text>
              <Text style={styles.resultScore}>{data.loveLanguageQuiz.results.qualityTime}</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Acts of Service:</Text>
              <Text style={styles.resultScore}>{data.loveLanguageQuiz.results.acts}</Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );

  const renderMiniOlympics = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Mini-Olympics</Text>
        <Text style={styles.sectionSubtitle}>Playful games: relay, flip cup, sock toss, music guessing</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Mini-Olympics</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Saturday afternoon"
            value={data.miniOlympics.when}
            onChangeText={(text: any) => updateMiniOlympics({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Backyard, Living room, Local park"
            value={data.miniOlympics.where}
            onChangeText={(text: any) => updateMiniOlympics({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Games Played:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Relay race, Flip cup, Sock toss, Music guessing game"
            value={data.miniOlympics.gamesPlayed.join(', ')}
            onChangeText={(text: string) => updateMiniOlympics({ gamesPlayed: text.split(',').map((g: string) => g.trim()) })}
            multiline
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Winner:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Player One, Player Two, It was a tie!"
            value={data.miniOlympics.winner}
            onChangeText={(text: any) => updateMiniOlympics({ winner: text })}
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt: any, index: number) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>

          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>

            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text: any) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderLoveLanguagePractice = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Love Language Practice</Text>
        <Text style={styles.sectionSubtitle}>Each partner lists 2 actions to meet partner's love language</Text>
      </View>

      {data.loveLanguagePractice.map((practice: any, index: number) => (
        <View key={index} style={styles.practiceCard}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Partner's Love Language:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Words of Affirmation, Physical Touch, Quality Time..."
              value={practice.partnerLoveLanguage}
              onChangeText={(text: any) => updateLoveLanguagePractice(index, { partnerLoveLanguage: text })}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Action 1:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Write them a love note every morning"
              value={practice.actionOne}
              onChangeText={(text: any) => updateLoveLanguagePractice(index, { actionOne: text })}
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Action 2:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Give them a 10-minute back massage"
              value={practice.actionTwo}
              onChangeText={(text: any) => updateLoveLanguagePractice(index, { actionTwo: text })}
              multiline
            />
          </View>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderLoveLanguageQuiz();
      case 1:
        return renderMiniOlympics();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderLoveLanguagePractice();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.pink }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 11: Love Languages</Text>
        <Text style={styles.headerSubtitle}>Discover and practice your love languages</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step: any, index: number) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.pink }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.pink,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  quizCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  progressBar: {
    marginBottom: 20,
  },
  progressText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBarFill: {
    height: 8,
    backgroundColor: colors.borderLight,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarProgress: {
    height: '100%',
    backgroundColor: colors.pink,
    borderRadius: 4,
  },
  questionText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 26,
  },
  answerSection: {
    gap: 16,
    marginBottom: 20,
  },
  playerAnswer: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  nextButton: {
    backgroundColor: colors.pink,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  nextButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  resultsSection: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightPink,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.pinkDark,
    marginBottom: 16,
    textAlign: 'center',
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightPink,
  },
  resultLabel: {
    fontSize: 14,
    color: colors.pinkDark,
    flex: 1,
  },
  resultScore: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.pinkDark,
    backgroundColor: colors.backgroundPink,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  practiceCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
