/**
 * Daily Questions Achievements Screen
 *
 * Displays achievements, streaks, and progress for daily questions.
 * Provides motivation and celebration for consistent participation.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { router } from 'expo-router';
import {
    ArrowLeft,
    Award,
    Calendar,
    Crown,
    Flame,
    Heart,
    MessageCircle,
    Star,
    Target,
    Trophy
} from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Animated,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { DSCard } from '../src/components/shared';
import { useDailyQuestions } from '../src/journeys/daily/useDailyQuestions';
import { useGlobalTheme as useTheme } from '../src/shared/components/common/ThemeProvider';
import HamburgerMenu from '../src/shared/components/layout/HamburgerMenu';
import { colors } from '../src/shared/utils/colors';
import { tokens } from '../src/utils/theme';

interface Achievement {
  id: string;
  title: string;
  description: string;
  emoji: string;
  icon: React.ReactNode;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
  category: 'streak' | 'participation' | 'engagement' | 'milestone';
}

export default function DailyQuestionsAchievementsScreen() {
  const { currentTheme } = useTheme();
  const { isLoading } = useDailyQuestions();

  // Mock data for now - these properties don't exist in the hook
  const streakData = {
    total_questions_answered: 0,
    current_streak: 0,
    longest_streak: 0
  };
  const questionHistory: any[] = [];
  const [selectedCategory, setSelectedCategory] = useState<'all' | Achievement['category']>('all');
  const [animatedValues] = useState(() =>
    Array.from({ length: 20 }, () => new Animated.Value(0))
  );

  // Generate achievements based on user data
  const generateAchievements = (): Achievement[] => {
    if (!streakData) return [];

    const achievements: Achievement[] = [
      // Streak Achievements
      {
        id: 'first_answer',
        title: 'Getting Started',
        description: 'Answer your first daily question',
        emoji: '💬',
        icon: <MessageCircle size={24} color={currentTheme.primary} />,
        unlocked: streakData.total_questions_answered >= 1,
        category: 'streak'
      },
      {
        id: 'three_day_streak',
        title: 'On Fire',
        description: 'Maintain a 3-day streak',
        emoji: '🔥',
        icon: <Flame size={24} color={currentTheme.error} />,
        unlocked: streakData.current_streak >= 3,
        category: 'streak'
      },
      {
        id: 'one_week_streak',
        title: 'One Week Strong',
        description: 'Maintain a 7-day streak',
        emoji: '🌟',
        icon: <Star size={24} color={currentTheme.warning} />,
        unlocked: streakData.current_streak >= 7,
        category: 'streak'
      },
      {
        id: 'two_week_streak',
        title: 'Building Momentum',
        description: 'Maintain a 14-day streak',
        emoji: '💪',
        icon: <Target size={24} color={currentTheme.primary} />,
        unlocked: streakData.current_streak >= 14,
        category: 'streak'
      },
      {
        id: 'one_month_streak',
        title: 'Relationship Champion',
        description: 'Maintain a 30-day streak',
        emoji: '🏆',
        icon: <Trophy size={24} color={currentTheme.warning} />,
        unlocked: streakData.current_streak >= 30,
        category: 'streak'
      },
      {
        id: 'hundred_day_streak',
        title: 'Incredible Dedication',
        description: 'Maintain a 100-day streak',
        emoji: '💎',
        icon: <Crown size={24} color={currentTheme.primary} />,
        unlocked: streakData.current_streak >= 100,
        category: 'streak'
      },

      // Participation Achievements
      {
        id: 'ten_questions',
        title: 'Getting Into It',
        description: 'Answer 10 daily questions',
        emoji: '📝',
        icon: <Calendar size={24} color={currentTheme.secondary} />,
        unlocked: streakData.total_questions_answered >= 10,
        progress: Math.min(streakData.total_questions_answered, 10),
        maxProgress: 10,
        category: 'participation'
      },
      {
        id: 'fifty_questions',
        title: 'Consistent Communicator',
        description: 'Answer 50 daily questions',
        emoji: '💬',
        icon: <MessageCircle size={24} color={currentTheme.secondary} />,
        unlocked: streakData.total_questions_answered >= 50,
        progress: Math.min(streakData.total_questions_answered, 50),
        maxProgress: 50,
        category: 'participation'
      },
      {
        id: 'hundred_questions',
        title: 'Daily Questions Master',
        description: 'Answer 100 daily questions',
        emoji: '🎯',
        icon: <Target size={24} color={currentTheme.secondary} />,
        unlocked: streakData.total_questions_answered >= 100,
        progress: Math.min(streakData.total_questions_answered, 100),
        maxProgress: 100,
        category: 'participation'
      },

      // Engagement Achievements
      {
        id: 'first_reaction',
        title: 'Sharing the Love',
        description: 'React to your partner\'s response',
        emoji: '❤️',
        icon: <Heart size={24} color={currentTheme.error} />,
        unlocked: false, // This would need to be tracked separately
        category: 'engagement'
      },
      {
        id: 'first_comment',
        title: 'Starting Conversations',
        description: 'Comment on your partner\'s response',
        emoji: '💭',
        icon: <MessageCircle size={24} color={currentTheme.primary} />,
        unlocked: false, // This would need to be tracked separately
        category: 'engagement'
      },

      // Milestone Achievements
      {
        id: 'longest_streak',
        title: 'Personal Best',
        description: `Achieve your longest streak: ${streakData.longest_streak} days`,
        emoji: '🏅',
        icon: <Award size={24} color={currentTheme.warning} />,
        unlocked: streakData.longest_streak > 0,
        category: 'milestone'
      }
    ];

    return achievements;
  };

  const achievements = generateAchievements();

  const filteredAchievements = achievements.filter(achievement =>
    selectedCategory === 'all' || achievement.category === selectedCategory
  );

  const unlockedAchievements = achievements.filter(a => a.unlocked);
  const lockedAchievements = achievements.filter(a => !a.unlocked);

  // Animate achievements on load
  useEffect(() => {
    const animations = animatedValues.map((value, index) =>
      Animated.timing(value, {
        toValue: 1,
        duration: 300,
        delay: index * 100,
        useNativeDriver: true,
      })
    );

    Animated.stagger(100, animations).start();
  }, []);

  const getCategoryColor = (category: Achievement['category']) => {
    switch (category) {
      case 'streak': return currentTheme.error;
      case 'participation': return currentTheme.primary;
      case 'engagement': return currentTheme.secondary;
      case 'milestone': return currentTheme.warning;
      default: return currentTheme.textSecondary;
    }
  };

  const getCategoryIcon = (category: Achievement['category']) => {
    switch (category) {
      case 'streak': return <Flame size={16} color={currentTheme.error} />;
      case 'participation': return <Target size={16} color={currentTheme.primary} />;
      case 'engagement': return <Heart size={16} color={currentTheme.secondary} />;
      case 'milestone': return <Trophy size={16} color={currentTheme.warning} />;
      default: return <Star size={16} color={currentTheme.textSecondary} />;
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
        <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            Loading achievements...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.background }]}>
      <HamburgerMenu visible={false} onClose={() => {}} menuItems={[]} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={currentTheme.textPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: currentTheme.textPrimary }]}>
          Daily Questions Achievements
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Stats Overview */}
        <DSCard style={styles.statsCard}>
          <View style={styles.statsHeader}>
            <Trophy size={24} color={currentTheme.primary} />
            <Text style={[styles.statsTitle, { color: currentTheme.textPrimary }]}>
              Your Progress
            </Text>
          </View>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: currentTheme.primary }]}>
                {unlockedAchievements.length}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Achievements Unlocked
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: currentTheme.error }]}>
                {streakData?.current_streak || 0}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Current Streak
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: currentTheme.warning }]}>
                {streakData?.longest_streak || 0}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Longest Streak
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: currentTheme.secondary }]}>
                {streakData?.total_questions_answered || 0}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Total Answered
              </Text>
            </View>
          </View>
        </DSCard>

        {/* Category Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryScroll}
          contentContainerStyle={styles.categoryContainer}
        >
          <TouchableOpacity
            style={[
              styles.categoryPill,
              selectedCategory === 'all' && styles.categoryPillActive
            ]}
            onPress={() => setSelectedCategory('all')}
          >
            <Text style={[
              styles.categoryPillText,
              selectedCategory === 'all' && styles.categoryPillTextActive
            ]}>
              All ({achievements.length})
            </Text>
          </TouchableOpacity>

          {['streak', 'participation', 'engagement', 'milestone'].map((category) => {
            const count = achievements.filter(a => a.category === category).length;
            return (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryPill,
                  selectedCategory === category && styles.categoryPillActive
                ]}
                onPress={() => setSelectedCategory(category as Achievement['category'])}
              >
                <Text style={[
                  styles.categoryPillText,
                  selectedCategory === category && styles.categoryPillTextActive
                ]}>
                  {category.charAt(0).toUpperCase() + category.slice(1)} ({count})
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>

        {/* Achievements List */}
        <View style={styles.achievementsList}>
          {filteredAchievements.map((achievement, index) => (
            <Animated.View
              key={achievement.id}
              style={[
                styles.achievementCard,
                {
                  opacity: animatedValues[index] || 0,
                  transform: [{
                    translateY: animatedValues[index]?.interpolate({
                      inputRange: [0, 1],
                      outputRange: [50, 0],
                    }) || 0
                  }]
                }
              ]}
            >
              <DSCard style={{
                ...styles.achievementCardInner,
                ...(!achievement.unlocked && styles.achievementCardLocked)
              }}>
                <View style={styles.achievementHeader}>
                  <View style={styles.achievementIcon}>
                    {achievement.unlocked ? (
                      <Text style={styles.achievementEmoji}>{achievement.emoji}</Text>
                    ) : (
                      <View style={[styles.lockedIcon, { backgroundColor: currentTheme.border }]}>
                        <Text style={styles.lockedEmoji}>🔒</Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.achievementInfo}>
                    <Text style={[
                      styles.achievementTitle,
                      { color: achievement.unlocked ? currentTheme.textPrimary : currentTheme.textSecondary }
                    ]}>
                      {achievement.title}
                    </Text>
                    <Text style={[
                      styles.achievementDescription,
                      { color: currentTheme.textSecondary }
                    ]}>
                      {achievement.description}
                    </Text>
                  </View>

                  <View style={styles.achievementCategory}>
                    {getCategoryIcon(achievement.category)}
                  </View>
                </View>

                {/* Progress Bar */}
                {achievement.progress !== undefined && achievement.maxProgress && (
                  <View style={styles.progressContainer}>
                    <View style={[styles.progressBar, { backgroundColor: currentTheme.border }]}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            backgroundColor: getCategoryColor(achievement.category),
                            width: `${(achievement.progress / achievement.maxProgress) * 100}%`
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: currentTheme.textSecondary }]}>
                      {achievement.progress}/{achievement.maxProgress}
                    </Text>
                  </View>
                )}
              </DSCard>
            </Animated.View>
          ))}
        </View>

        {/* Motivation Message */}
        <DSCard style={styles.motivationCard}>
          <View style={styles.motivationContent}>
            <Text style={styles.motivationEmoji}>💕</Text>
            <Text style={[styles.motivationTitle, { color: currentTheme.textPrimary }]}>
              Keep Going!
            </Text>
            <Text style={[styles.motivationText, { color: currentTheme.textSecondary }]}>
              Every question you answer together strengthens your bond.
              {lockedAchievements.length > 0 && ` You're ${lockedAchievements.length} achievements away from unlocking more rewards!`}
            </Text>
          </View>
        </DSCard>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.lg,
    paddingVertical: tokens.spacing.md,
  },
  backButton: {
    padding: tokens.spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: tokens.spacing.lg,
  },
  statsCard: {
    marginBottom: tokens.spacing.lg,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.lg,
  },
  statsTitle: {
    marginLeft: tokens.spacing.sm,
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  statNumber: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.bold,
    marginBottom: tokens.spacing.xs,
  },
  statLabel: {
    fontSize: tokens.typography.fontSize.sm,
    textAlign: 'center',
  },
  categoryScroll: {
    marginBottom: tokens.spacing.lg,
  },
  categoryContainer: {
    paddingHorizontal: 0,
    gap: tokens.spacing.sm,
  },
  categoryPill: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    borderRadius: tokens.radii.pill,
  },
  categoryPillActive: {
    backgroundColor: colors.primary,
  },
  categoryPillText: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    color: colors.textSecondary,
  },
  categoryPillTextActive: {
    color: 'white',
  },
  achievementsList: {
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
  },
  achievementCard: {
    // Animation container
  },
  achievementCardInner: {
    // Card content
  },
  achievementCardLocked: {
    opacity: 0.6,
  },
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementIcon: {
    marginRight: tokens.spacing.md,
  },
  achievementEmoji: {
    fontSize: 32,
  },
  lockedIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  lockedEmoji: {
    fontSize: 16,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.xs,
  },
  achievementDescription: {
    fontSize: tokens.typography.fontSize.sm,
    lineHeight: tokens.typography.lineHeight.tight,
  },
  achievementCategory: {
    marginLeft: tokens.spacing.sm,
  },
  progressContainer: {
    marginTop: tokens.spacing.md,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: tokens.spacing.xs,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: tokens.typography.fontSize.xs,
    textAlign: 'right',
  },
  motivationCard: {
    marginBottom: tokens.spacing.xl,
  },
  motivationContent: {
    alignItems: 'center',
    textAlign: 'center',
  },
  motivationEmoji: {
    fontSize: 48,
    marginBottom: tokens.spacing.md,
  },
  motivationTitle: {
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.sm,
  },
  motivationText: {
    fontSize: tokens.typography.fontSize.md,
    lineHeight: tokens.typography.lineHeight.normal,
    textAlign: 'center',
  },
});
