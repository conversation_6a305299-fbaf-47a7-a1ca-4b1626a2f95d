import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, BookOpen, Calendar, CheckCircle, MessageCircle, Play, Save, Users } from 'lucide-react-native';
import { DSBadge, DSHeaderBar, RelationshipActivityType } from '../src/components/shared';
import { usePointsSystemSupabase } from '../src/hooks/usePointsSystemSupabase';
import { useUserProfile } from '../src/hooks/useUserProfile';
import { useWeeklyDateNightIntegration } from '../src/journeys/activities/useWeeklyDateNightIntegration';
import { useWeekOneData } from '../src/journeys/daily/useWeekOneData';
import { colors } from '../src/shared/utils/colors';
import type { GameActivityResponse } from '../types';


export default function WeekOneScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isPlayerOneTurn, setIsPlayerOneTurn] = useState(true);

  // Relationship Activities Module states
  const [currentRelationshipActivity, setCurrentRelationshipActivity] = useState<RelationshipActivityType | null>(null);
  const [isRelationshipActivityActive, setIsRelationshipActivityActive] = useState(false);

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);

  const {
    data,
    updateMatchGameResponse,
    updateDateNightPlan,
    updateChatPrompt,
    updateSoftStartupPractice,
    updateCompletedSections,
  } = useWeekOneData();

  const { addPoints } = usePointsSystemSupabase();
  const { profile, getPartnerNames } = useUserProfile();
  const { completeWeeklyDateNight } = useWeeklyDateNightIntegration();

  const steps = [
    { title: 'The Match Activity', icon: <Users size={24} color={colors.white} />, activityName: 'The Match Activity' },
    { title: 'Date Night Plan', icon: <Calendar size={24} color={colors.white} />, activityName: 'Date Night Plan' },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} />, activityName: 'Chat Prompts' },
    { title: 'Soft Start-Up', icon: <BookOpen size={24} color={colors.white} />, activityName: 'Soft Start-Up' },
  ];

  const handleSaveAndContinue = async () => {
    try {
      // Mark current activity as completed and award points
      const result = await addPoints(25, 'activity');

      if (currentStep < steps.length - 1) {
        updateCompletedSections(currentStep, true);
        setCurrentStep(currentStep + 1);

        // Show points earned
        Alert.alert(
          'Activity Completed!',
          `Great job! You earned 25 points!`
        );
      } else {
        // All steps completed
        updateCompletedSections(currentStep, true);

        const finalResult = await addPoints(25, 'activity');
        Alert.alert(
          'Congratulations!',
          `You've completed Week One! All your responses have been saved to your scrapbook.\n\nFinal activity: +25 points!`
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  // Relationship Activity Match Activity
  const createMatchActivity = (): RelationshipActivityType => ({
    id: 'week1_match_game',
    title: 'The Match Game',
    description: 'How well do you know each other?',
    type: 'match_game',
    prompts: [
      { id: 'comfort_meal', question: 'My comfort meal', type: 'guess', category: 'Food', difficulty: 'easy' },
      { id: 'movie_rewatch', question: 'A movie I could rewatch over and over', type: 'guess', category: 'Entertainment', difficulty: 'easy' },
      { id: 'guilty_pleasure', question: 'My guilty pleasure', type: 'guess', category: 'Personal', difficulty: 'medium' },
      { id: 'dance_song', question: 'If this song is on, I\'m on the dance floor', type: 'guess', category: 'Music', difficulty: 'easy' },
      { id: 'dream_vacation', question: 'My dream vacation', type: 'guess', category: 'Travel', difficulty: 'easy' },
    ],
    responses: []
  });

  // Relationship Activity Date Night Plan activity
  const createDateNightPlanActivity = (): RelationshipActivityType => ({
    id: 'week1_date_night_plan',
    title: 'Date Night Plan',
    description: 'Game Night – The Bet Is On',
    type: 'date_night_plan',
    prompts: [
      { id: 'top_3_games', question: 'What are your top 3 board games to play?', type: 'list', category: 'Planning', difficulty: 'easy' },
      { id: 'bet_ideas', question: 'What playful bets or dares would you suggest for the loser?', type: 'list', category: 'Planning', difficulty: 'medium' },
      { id: 'snack_preferences', question: 'What snacks and drinks should we have ready?', type: 'list', category: 'Planning', difficulty: 'easy' },
    ],
    responses: []
  });

  // Relationship Activity Chat Prompts activity
  const createChatPromptsActivity = (): RelationshipActivityType => ({
    id: 'week1_chat_prompts',
    title: 'Date Night Chat Prompts',
    description: 'Deep conversations over dinner',
    type: 'chat_prompts',
    prompts: [
      { id: 'happiness', question: 'Happiness is...', type: 'text', category: 'Reflection', difficulty: 'easy' },
      { id: 'wish_time', question: 'I wish I made more time for...', type: 'text', category: 'Reflection', difficulty: 'medium' },
      { id: 'dream_future', question: 'In 5 years, I see us...', type: 'text', category: 'Future', difficulty: 'medium' },
      { id: 'grateful_for', question: 'I\'m most grateful for you because...', type: 'text', category: 'Appreciation', difficulty: 'easy' },
    ],
    responses: []
  });

  const handleStartRelationshipGameActivity = (activityType: 'matchGame' | 'dateNightPlan' | 'chatPrompts') => {
    let activity: RelationshipActivityType;
    if (activityType === 'matchGame') {
      activity = createMatchActivity();
    } else if (activityType === 'dateNightPlan') {
      activity = createDateNightPlanActivity();
    } else {
      activity = createChatPromptsActivity();
    }
    setCurrentRelationshipActivity(activity);
    setIsRelationshipActivityActive(true);
  };

  const handleRelationshipGameComplete = async (completedActivity: RelationshipActivityType) => {
    try {
      // Mark the appropriate section as completed
      if (completedActivity.id === 'week1_match_game') {
        updateCompletedSections(0, true);

        // Award points for Match Game
        const result = await addPoints(25, 'activity');
        Alert.alert(
          'Match Game Completed! 🎉',
          `Great job! You earned 15 points!`
        );
      } else if (completedActivity.id === 'week1_date_night_plan') {
        updateCompletedSections(1, true);

        // Award points for Date Night Plan
        const result = await addPoints(20, 'activity');

        // Integrate with global Date Night pool
        await completeWeeklyDateNight(1);

        Alert.alert(
          'Date Night Plan Completed! 🎉',
          `Great job! You earned 20 points! This Date Night idea has been added to your global Date Night pool for future reference!`
        );
      } else if (completedActivity.id === 'week1_chat_prompts') {
        updateCompletedSections(2, true);

        // Award points for Chat Prompts
        const result = await addPoints(15, 'activity');
        Alert.alert(
          'Chat Prompts Completed! 🎉',
          `Great job! You earned 15 points!`
        );
      }

      // Save responses to week data
      completedActivity.responses?.forEach((response: any) => {
        if (completedActivity.id === 'week1_match_game') {
          // Find the matching question by promptId and update
          const questionIndex = data.matchGameResponses.findIndex((q: any, index: number) => {
            // Map promptId to question index based on the order
            const promptIdToIndex: { [key: string]: number } = {
              'comfort_meal': 0,
              'movie_rewatch': 1,
              'guilty_pleasure': 2,
              'dance_song': 3,
              'dream_vacation': 4
            };
            return promptIdToIndex[response.promptId] === index;
          });

          if (questionIndex !== -1) {
            updateMatchGameResponse(questionIndex, {
              playerOneAnswer: response.partner1Answer,
              playerTwoGuess: response.partner2Answer,
              isRevealed: true
            });
          }
        }
      });

      setCurrentRelationshipActivity(null);
      setIsRelationshipActivityActive(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  const handleSaveToScrapbook = (responses: GameActivityResponse[]) => {
    // This integrates with the existing scrapbook system
    console.log('Saving to scrapbook:', responses);
  };

  const handleMatchGameInput = (text: string) => {
    if (isPlayerOneTurn) {
      updateMatchGameResponse(currentQuestionIndex, { playerOneAnswer: text });
    } else {
      updateMatchGameResponse(currentQuestionIndex, { playerTwoGuess: text });
    }
  };

  const handleReveal = () => {
    updateMatchGameResponse(currentQuestionIndex, { isRevealed: true });
  };

  const handleNextQuestion = async () => {
    if (currentQuestionIndex < data.matchGameResponses.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setIsPlayerOneTurn(!isPlayerOneTurn);
    } else {
      // Game completed - mark as completed and award points
      try {
        const result = await addPoints(25, 'activity');
        updateCompletedSections(0, true);

        Alert.alert(
          'Match Game Completed!',
          `Great job! You earned 15 points!`
        );
      } catch (error) {
        Alert.alert('Error', 'Failed to save progress. Please try again.');
      }
    }
  };

  // Check if current step is completed
  const isStepCompleted = (stepIndex: number) => {
    return data.completedSections[stepIndex];
  };

  const renderMatchGame = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>The Match Game</Text>
          <Text style={styles.sectionSubtitle}>How well do you know each other?</Text>
        </View>

        <View style={styles.gameInstructions}>
          <Text style={styles.instructionText}>
            Both partners answer independently, then reveal together!
          </Text>
          <Text style={styles.instructionText}>
            One partner writes their answer, the other guesses what they said
          </Text>
        </View>

        <TouchableOpacity
          style={styles.startActivityButton}
          onPress={() => handleStartRelationshipGameActivity('matchGame')}
        >
          <View
            style={[styles.startActivityButtonGradient, { backgroundColor: colors.primary }]}
          >
            <Play size={20} color={colors.white} />
            <Text style={styles.startActivityButtonText}>Play The Match Game</Text>
          </View>
        </TouchableOpacity>

        {data.completedSections[0] && (
          <View style={styles.completedSection}>
            <CheckCircle size={24} color={colors.success} />
            <Text style={styles.completedText}>Match Game completed! Check your scrapbook for results.</Text>
          </View>
        )}
      </View>
    );
  };

  const renderDateNightPlan = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Date Night Plan</Text>
        <Text style={styles.sectionSubtitle}>Game Night – The Bet Is On</Text>
      </View>

      <View style={styles.planCard}>
        <Text style={styles.planDescription}>
          Play your favorite board game with a twist: loser of each round makes the winner a drink,
          grabs a snack, or takes on a playful dare.
        </Text>

        <TouchableOpacity
          style={styles.startActivityButton}
          onPress={() => handleStartRelationshipGameActivity('dateNightPlan')}
        >
          <View
            style={[styles.startActivityButtonGradient, { backgroundColor: colors.primary }]}
          >
            <Play size={20} color={colors.white} />
            <Text style={styles.startActivityButtonText}>Plan Together</Text>
          </View>
        </TouchableOpacity>

        {data.completedSections[1] && (
          <View style={styles.completedSection}>
            <CheckCircle size={24} color={colors.success} />
            <Text style={styles.completedText}>Date Night Plan completed! Check your scrapbook for shared ideas.</Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Date Night Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations over dinner</Text>
      </View>

      <View style={styles.planCard}>
        <Text style={styles.planDescription}>
          Answer these prompts independently, then reveal together to start meaningful conversations.
        </Text>

        <TouchableOpacity
          style={styles.startActivityButton}
          onPress={() => handleStartRelationshipGameActivity('chatPrompts')}
        >
          <View
            style={[styles.startActivityButtonGradient, { backgroundColor: colors.primary }]}
          >
            <Play size={20} color={colors.white} />
            <Text style={styles.startActivityButtonText}>Start Chat Prompts</Text>
          </View>
        </TouchableOpacity>

        {data.completedSections[2] && (
          <View style={styles.completedSection}>
            <CheckCircle size={24} color={colors.success} />
            <Text style={styles.completedText}>Chat Prompts completed! Check your scrapbook for conversation starters.</Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderSoftStartup = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Relationship Skills Toolkit</Text>
        <Text style={styles.sectionSubtitle}>Soft Start-Up Technique</Text>
      </View>

      <View style={styles.skillCard}>
        <Text style={styles.skillIntro}>
          Start conversations gently using 'I' statements without blame or criticism.
        </Text>

        <View style={styles.examplesContainer}>
          <Text style={styles.examplesTitle}>Examples:</Text>
          <View style={styles.exampleItem}>
            <Text style={styles.exampleLabel}>Instead of:</Text>
            <Text style={styles.exampleText}>"You always ignore my suggestions"</Text>
            <Text style={styles.exampleLabel}>Try:</Text>
            <Text style={styles.exampleText}>"I feel unheard when my suggestions aren't acknowledged."</Text>
          </View>

          <View style={styles.exampleItem}>
            <Text style={styles.exampleLabel}>Instead of:</Text>
            <Text style={styles.exampleText}>"You never help with chores"</Text>
            <Text style={styles.exampleLabel}>Try:</Text>
            <Text style={styles.exampleText}>"I feel stressed when I have to handle everything myself."</Text>
          </View>
        </View>

        <Text style={styles.practiceTitle}>Practice Time:</Text>
        <Text style={styles.practiceSubtitle}>Rewrite these statements using "I" statements:</Text>

        {data.softStartupPractice.map((practice: any, index: number) => (
          <View key={index} style={styles.practiceItem}>
            <Text style={styles.originalStatement}>{practice.originalStatement}</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Rewrite using 'I' statements..."
              value={practice.practiceStatement}
              onChangeText={(text: any) => updateSoftStartupPractice(index, { practiceStatement: text })}
              multiline
            />
          </View>
        ))}
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderMatchGame();
      case 1:
        return renderDateNightPlan();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderSoftStartup();
      default:
        return null;
    }
  };

  const completedCount = data.completedSections.filter(Boolean).length;

  // Navigate to Games Hub if relationship game is active
  if (isRelationshipActivityActive && currentRelationshipActivity) {
    // Navigate to Activities tab (games hub)
    router.push('/(tabs)/activities');
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <DSHeaderBar
        title="Week One - The Match Game"
        tone="sage"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
        right={
          <DSBadge label={`${completedCount}/4`} tone="neutral" />
        }
      />

      {/* Progress Steps */}
      <View style={styles.stepsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step: any, index: number) => (
            <TouchableOpacity
              key={index}
                             style={[
                 styles.stepIndicator,
                 index === currentStep && styles.stepIndicatorActive,
                 data.completedSections[index] && styles.stepIndicatorCompleted
               ]}
              onPress={() => setCurrentStep(index)}
            >
                             {data.completedSections[index] ? (
                 <CheckCircle size={16} color={colors.white} />
               ) : (
                 step.icon
               )}
               <Text style={[
                 styles.stepText,
                 index === currentStep && styles.stepTextActive,
                 data.completedSections[index] && styles.stepTextCompleted
               ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.saveButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.saveButtonGradient, { backgroundColor: colors.primary }]}
          >
            <Save size={20} color={colors.white} />
            <Text style={styles.saveButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week One!'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
  },
  progressBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  progressBadgeText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  stepsContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.backgroundTertiary,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  stepIndicatorActive: {
    backgroundColor: colors.primary,
  },
  stepIndicatorCompleted: {
    backgroundColor: colors.success,
  },
  stepText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  stepTextActive: {
    color: colors.white,
  },
  stepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  gameInstructions: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 4,
  },
  questionCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  questionText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInput: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: colors.backgroundGray,
  },
  revealButton: {
    marginTop: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  revealButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  revealButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  revealSection: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  revealTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  answerText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  answerLabel: {
    fontWeight: '600',
    color: colors.textPrimary,
  },
  nextButton: {
    marginTop: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  nextButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  nextButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  progressIndicator: {
    alignItems: 'center',
    marginTop: 16,
  },
  progressText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  planCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  planDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: colors.backgroundGray,
  },
  completeButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 8,
  },
  completeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
  },
  completeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  promptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  promptInputs: {
    gap: 16,
  },
  skillCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  skillIntro: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  examplesContainer: {
    marginBottom: 20,
  },
  examplesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  exampleItem: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: colors.backgroundGray,
    borderRadius: 12,
  },
  exampleLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  exampleText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  practiceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  practiceSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  practiceItem: {
    marginBottom: 16,
  },
  originalStatement: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  practiceInput: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 60,
    textAlignVertical: 'top',
    backgroundColor: colors.backgroundGray,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.backgroundTertiary,
  },
  comeBackButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  gameStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.backgroundGray,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  startActivityButton: {
    marginTop: 20,
    marginBottom: 20,
  },
  startActivityButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  startActivityButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  completedSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: colors.success + '20',
    borderRadius: 12,
    marginTop: 16,
    gap: 8,
  },
  completedText: {
    fontSize: 16,
    color: colors.success,
    fontWeight: '500',
    textAlign: 'center',
  },
});
